from enum import IntEnum
from pathlib import Path
from feasytools.pdf import *
from fpowerkit import Grid
from sumolib.net import readNet, Net
from typing import TypeVar

import random

from ..traffic import DetectFiles, GetTimeAndNetwork
from .graph import RoadNetConnectivityChecker as ELGraph

from .tripgen import EVsGenerator

DEFAULT_CNAME = str(Path(__file__).parent.parent / "probtable")

class ProcExisting(IntEnum):
    """处理已存在文件的方式"""

    OVERWRITE = 0  # 覆盖
    SKIP = 1  # 跳过
    BACKUP = 2  # 备份
    EXCEPTION = 3  # 抛出异常

    def do(self, path: str):
        if self == ProcExisting.OVERWRITE:
            Path(path).unlink()
        elif self == ProcExisting.SKIP:
            pass
        elif self == ProcExisting.BACKUP:
            i = 0
            while Path(f"{path}.{i}.bak").exists():
                i += 1
            Path(path).rename(f"{path}.{i}.bak")
        else:
            raise FileExistsError(f"错误: 文件已存在: {path}")

    def check(self, path: str):
        if Path(path).exists():
            self.do(path)


T = TypeVar("T")

class ListSelection(IntEnum):
    """列表选择方法"""

    ALL = 0  # 全部
    RANDOM = 1  # 随机
    GIVEN = 2  # 指定

    def select(self, lst: list[T], n: int = -1, given: list[T] = []) -> list[T]:
        if self == ListSelection.ALL:
            return lst
        elif self == ListSelection.RANDOM:
            if n == -1:
                raise ValueError("错误: 当选择随机时必须指定n")
            return random.sample(lst, n)
        else:
            return given


class PricingMethod(IntEnum):
    """定价方法"""
    FIXED = 0  # 固定价格
    RANDOM = 1  # 5档随机价格


class TrafficGenerator:
    def __init__(
        self,
        root: str,
        silent: bool = False,
        existing: ProcExisting = ProcExisting.BACKUP,
    ):
        """
        交通生成器初始化
            root: 根目录
            silent: 是否静默模式
            existing: 如何处理已存在的文件
        """
        self.__root = root
        self.__cfg = DetectFiles(root)
        self.__name = self.__cfg["name"]
        self.__silent = silent
        self.__existing = existing
        if not self.__cfg.cfg:
            raise FileNotFoundError("错误: 未指定SUMO配置文件.")
        self.__start_time, self.__end_time, self.__rnet_file = GetTimeAndNetwork(self.__cfg.cfg)
        if self.__rnet_file is not None:
            self.__cfg.net = str(Path(self.__cfg.cfg).parent / self.__rnet_file)
        if not self.__cfg.net:
            raise FileNotFoundError("错误: 未指定路网文件.")
        self.__rnet: Net = readNet(self.__cfg.net)
        self.__edges: list[str] = [e.getID() for e in self.__rnet.getEdges()]
        self.__ava_fcs: list[str] = [
            e for e in self.__edges
            if e.upper().startswith("CS") and not e.lower().endswith("rev")
        ]
        self.__ava_scs: list[str] = [
            e for e in self.__edges if not e.upper().startswith("CS")
        ]
        
        self.__bus_names = ["None"]
        if self.__cfg.grid: 
            self.__bus_names = Grid.fromFile(self.__cfg.grid).BusNames
        else:
            print("Grid is not defined, and thus buses are not included. CS generation may meet errors.")
        
        if "cscsv" in self.__cfg:
            self.__cs_file = self.__cfg["cscsv"]
        else:
            self.__cs_file = ""
        
        if "grid" in self.__cfg:
            self.__grid_file = self.__cfg["grid"]
        else:
            self.__grid_file = ""


    
    def EVTrips(self, n: int, seed: int,
            day_count: int = 7,
            cname: str = DEFAULT_CNAME,
        **kwargs):
        """
        生成车辆行程（同时生成工作日和周末两个文件）
            n: 车辆数量
            seed: 随机种子
            day_count: 天数
            cname: 行程参数文件夹
            v2g_prop: 愿意参与V2G的用户比例
            omega: PDFunc | None = None,
            krel: PDFunc | None = None,
            ksc: PDFunc | None = None,
            kfc: PDFunc | None = None,
            kv2g: PDFunc | None = None
        """
        if "veh" in self.__cfg:
            self.__existing.do(self.__cfg["veh"])

        # 生成工作日车辆文件
        fname_weekday = f"{self.__root}/{self.__name}.weekday.veh.xml.gz"
        print("生成工作日车辆文件...")
        EVsGenerator(cname, self.__root, seed, is_weekday=True).genEVs(
            n, fname_weekday, day_count, self.__silent, **kwargs)

        # 生成周末车辆文件
        fname_weekend = f"{self.__root}/{self.__name}.weekend.veh.xml.gz"
        print("生成周末车辆文件...")
        EVsGenerator(cname, self.__root, seed, is_weekday=False).genEVs(
            n, fname_weekend, day_count, self.__silent, **kwargs)

        print(f"车辆文件生成完成:")
        print(f"  工作日文件: {fname_weekday}")
        print(f"  周末文件: {fname_weekend}")

        


