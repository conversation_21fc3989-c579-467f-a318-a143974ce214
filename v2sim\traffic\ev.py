"""
电动汽车模块
包含行程、车辆状态和电动汽车类的定义
"""
from __future__ import annotations
import enum, math
from typing import Callable, Iterable, List, Tuple, Union, Optional
from feasytools import RangeList
from .utils import IntPairList


class Trip:
    """行程类，表示车辆的一次出行"""
    def __init__(
        self, trip_id: str, depart_time: int, fromTAZ: str, toTAZ: str, route: list[str], fixed_route: Optional[bool] = None
    ):
        self.ID = trip_id                    # 行程ID
        self.depart_time = depart_time       # 出发时间
        self.from_TAZ = fromTAZ             # 起点TAZ
        self.to_TAZ = toTAZ                 # 终点TAZ
        assert isinstance(route, list) and len(route) >= 2, "路径应该是包含至少2个元素的列表"
        self.route = route                   # 路径列表
        if fixed_route:
            self.fixed_route = True
        elif fixed_route is None:
            self.fixed_route = len(route) >= 2
        else:
            self.fixed_route = True

    @property
    def depart_edge(self):
        """出发边"""
        return self.route[0]

    @property
    def arrive_edge(self):
        """到达边"""
        return self.route[-1]

    def __repr__(self):
        return str(self)

    def __str__(self):
        return f"{self.depart_edge}->{self.arrive_edge}@{self.depart_time}"
    

class VehStatus(enum.IntEnum):
    """车辆状态枚举"""
    Driving = 0    # 行驶中
    Pending = 1    # 等待启动
    Charging = 2   # 充电中
    Parking = 3    # 停车中
    Depleted = 4   # 电量耗尽

def _EqualChargeRate(rate: float, ev: 'EV') -> float:
    """等速充电速率函数"""
    return rate

def _LinearChargeRate(rate: float, ev: 'EV') -> float:
    """线性充电速率函数，SOC>80%时速率下降"""
    if ev.SOC <= 0.8:
        return rate
    return rate * (3.4 - 3 * ev.SOC)

class ChargeRatePool:
    """充电速率修正函数池"""
    _pool: 'dict[str,Callable[[float, EV], float]]' = {
        "Equal": _EqualChargeRate,   # 等速充电
        "Linear": _LinearChargeRate, # 线性充电
    }

    @staticmethod
    def add(name: str, func: 'Callable[[float, EV], float]'):
        """添加充电速率修正函数"""
        ChargeRatePool._pool[name] = func

    @staticmethod
    def get(name: str) -> 'Callable[[float, EV], float]':
        """获取充电速率修正函数"""
        return ChargeRatePool._pool[name]

class EV:
    """电动汽车类"""
    def __init__(
        self,
        id: str,
        trips: Iterable[Trip],
        eta_c: float,
        eta_d: float,
        bcap: float,
        soc: float,
        c: float,
        rf: float,
        rs: float,
        rv: float,
        omega: float,
        kr: float,
        kf: float,
        ks: float,
        kv: float,
        rmod: str = "Linear",
        sc_time: Union[None, IntPairList, RangeList] = None,
        max_sc_cost: float = 100.0,
        v2g_time: Union[None, IntPairList, RangeList] = None,
        min_v2g_earn: float = 0.0,
        cache_route: bool = False,
        vtype_id: int = -1,
        vtype_category: str = "general",
    ):
        self._id = id                   # 车辆ID
        self._sta = VehStatus.Parking   # 车辆状态
        self._cs = None                 # 目标充电站
        self._cost = 0                  # 车辆总充电费用，$
        self._earn = 0                  # 车辆总放电收益，$

        # 车辆类型信息
        self._vtype_id = vtype_id       # 车辆类型ID
        self._vtype_category = vtype_category  # 车辆类别



        self._bcap = bcap               # 电池容量，kWh
        assert 0.0 <= soc <= 1.0
        self._elec = soc * bcap         # 当前电池电量，kWh
        self._consumption = c / 1000    # 单位距离能耗，kWh/m
        self._efc_rate = rf / 3600      # 期望快充功率，kWh/s
        self._esc_rate = rs / 3600      # 期望慢充功率，kWh/s
        self._v2g_rate = rv / 3600      # 最大反向功率，kWh/s
        self._eta_charge = eta_c        # 充电效率
        self._eta_discharge = eta_d     # 放电效率
        self._chrate_mod = ChargeRatePool.get(rmod)
                                        # 充电速率修正函数
        self._sc_time = sc_time if isinstance(sc_time, RangeList) else RangeList(sc_time)
                                        # 慢充时间范围列表，None表示全天
        self._max_sc_cost = max_sc_cost # 最大慢充费用，$/kWh
        self._v2g_time = v2g_time if isinstance(v2g_time, RangeList) else RangeList(v2g_time)
                                        # V2G时间范围列表，None表示全天
        self._min_v2g_earn = min_v2g_earn
                                        # 最小V2G收益，$/kWh

        self._rate = 0                  # 实际充电功率，kWh/s
        self._chtar = bcap              # 快充时，离开前充电至多少kWh

        self._dis = 0                   # 行驶距离，m
        self._trips = tuple(trips)      # 车辆行程列表
        self._trip_index = 0            # 当前行程编号（索引）

        self._w = omega                 # 决策参数
        assert 1 <= kr <= 2
        self._krel = kr                 # 容忍系数
        assert 0 < kf < 1
        self._kfc = kf                  # 用户选择快充的荷电状态阈值
        assert kf <= ks < 1
        self._ksc = ks                  # 用户选择慢充的荷电状态阈值
        assert ks < kv
        self._kv2g = kv                 # 用户愿意参与V2G的荷电状态阈值

        self._cache_route = cache_route # 是否缓存路径

    @property
    def estimated_charge_time(self) -> float:
        """
        在当前充电水平、目标充电水平和充电速率下完成充电所需的时间
        """
        assert self._rate is not None
        if self._rate > 0:
            return max((self._chtar - self._elec) / self._rate, 0)
        else:
            return math.inf

    @property
    def elec(self) -> float:    # 当前电池电量 kWh
        return self._elec

    @property
    def SOC(self) -> float:
        """电池荷电状态（百分比）"""
        return self._elec / self._bcap

    @property
    def vtype_id(self) -> int:
        """车辆类型ID"""
        return self._vtype_id

    @property
    def vtype_category(self) -> str:
        """车辆类别"""
        return self._vtype_category

    @property
    def omega(self) -> float:
        """选择快充站的决策参数"""
        return self._w

    @omega.setter
    def omega(self, val: float):
        self._w = val

    @property
    def krel(self) -> float:
        """容忍系数"""
        return self._krel

    @krel.setter
    def krel(self, val: float):
        assert 1.0 <= val <= 2.0
        self._krel = val

    @property
    def kfc(self) -> float:
        """选择快充的荷电状态阈值"""
        return self._kfc

    @kfc.setter
    def kfc(self, val: float):
        assert 0.0 <= val <= self._ksc
        self._kfc = val

    @property
    def ksc(self) -> float:
        """选择慢充的荷电状态阈值"""
        return self._ksc

    @ksc.setter
    def ksc(self, val: float):
        assert self._kfc <= val <= 1.0
        self._ksc = val

    @property
    def kv2g(self) -> float:
        """选择V2G的荷电状态阈值"""
        return self._kv2g

    @kv2g.setter
    def kv2g(self, val: float):
        assert self._ksc < val
        self._kv2g = val

    @property
    def charge_target(self) -> float:
        """
        充电目标，即充电到此电量后离开快充站 (kWh)
        """
        return self._chtar

    @charge_target.setter
    def charge_target(self, val):
        assert 0 <= val <= self._bcap
        self._chtar = val

    @property
    def eta_charge(self) -> float:
        """车辆充电效率"""
        return self._eta_charge

    @property
    def eta_discharge(self) -> float:
        """车辆放电效率"""
        return self._eta_discharge

    @property
    def rate(self) -> float:
        """车辆期望充电速率，kWh/s"""
        return self._rate

    @property
    def max_v2g_rate(self) -> float:
        """车辆最大V2G反向功率速率，kWh/s"""
        return self._v2g_rate

    def stop_charging(self):
        """停止充电：将充电速率设为0"""
        self._rate = 0

    @property
    def status(self) -> VehStatus:
        """当前车辆状态"""
        return self._sta

    @status.setter
    def status(self, val):
        self._sta = val

    @property
    def target_CS(self) -> Union[None, str]:
        """
        目标快充站名称。当此项为None时，表示未被引导到充电站
        """
        return self._cs

    @target_CS.setter
    def target_CS(self, val):
        self._cs = val

    @property
    def ID(self) -> str:
        """车辆字符串ID"""
        return self._id

    @property
    def full_battery(self) -> float:
        """车辆电池容量 kWh"""
        return self._bcap

    @property
    def battery(self) -> float:
        """车辆电池电量 kWh"""
        return self._elec

    @property
    def consumption(self) -> float:
        """车辆能耗 kWh/m"""
        return self._consumption

    @property
    def odometer(self) -> float:
        """
        车辆在此次行程中行驶的距离（m），注意离开充电站被视为新行程
        """
        return self._dis

    @property
    def minimum_v2g_earn(self) -> float:
        """用户愿意参与V2G的最小收益，$/kWh"""
        return self._min_v2g_earn
    
    @property
    def maximum_slow_charge_cost(self) -> float:
        """愿意参与慢充的最大充电费用，$/kWh"""
        return self._max_sc_cost
    
    @property
    def v2g_time(self) -> RangeList:
        """用户愿意参与V2G的时间范围。None表示全天"""
        return self._v2g_time
    
    @property
    def slow_charge_time(self) -> RangeList:
        """用户愿意参与慢充的时间范围。None表示全天"""
        return self._sc_time
    
    def clear_odometer(self):
        """行程开始前，清零里程表"""
        self._dis = 0

    def drive(self, new_dis: float):
        """
        在行驶状态下更新电池荷电状态和里程表
        """
        # 由于SUMO保存和加载可能导致错误，所以-1.0来容忍误差
        assert new_dis >= self._dis - 1.0, f"EV {self._id}: self._dis = {self._dis:.8f} > new_dis = {new_dis:.8f}"
        self._elec -= (new_dis - self._dis) * self._consumption
        self._dis = new_dis

    def charge(self, sec: int, unit_cost: float, rate:float) -> float:
        """
        以当前充电速率为电池充电sec秒，
        返回实际充电量(kWh)（考虑损耗）
        """
        _elec = self._elec
        self._rate = self._chrate_mod(rate, self)
        self._elec += self._rate * sec * self._eta_charge
        if self._elec > self._bcap:
            self._elec = self._bcap
        if self._elec < 0:
            self._elec = 0
        delta_elec = self._elec - _elec
        self._cost += (delta_elec / self._eta_charge) * unit_cost
        return delta_elec

    def discharge(self, k: float, sec: int, unit_earn: float) -> float:
        """
        以k倍当前放电速率为电池放电sec秒，
        返回实际放电量kWh（考虑损耗）
        """
        # assert 0<=k<=1
        # if self.SOC <= self._kv2g: return 0
        _elec = self._elec
        self._elec -= self._v2g_rate * sec * k
        if self.SOC <= self._kv2g:
            self._elec = self._bcap * self._kv2g
        delta_elec = (_elec - self._elec) * self._eta_discharge
        assert delta_elec >= 0
        self._earn += delta_elec * unit_earn
        return delta_elec

    def willing_to_v2g(self, t:int, e:float) -> bool:
        """
        用户判断车辆是否愿意参与V2G
            t: 当前时间
            e: 当前V2G收益，$/kWh
        """
        # 使用新的充电决策模块进行V2G意愿评估
        from .charging_decision import ChargingWillingnessEvaluator
        evaluator = ChargingWillingnessEvaluator()
        return evaluator.evaluate_v2g_willingness(self, t, e)
    
    def willing_to_slow_charge(self, t: int, c: float, is_weekday: bool = True) -> bool:
        """
        用户决定车辆是否愿意慢充
            t: 当前时间
            c: 当前慢充费用，$/kWh
            is_weekday: 是否为工作日
        """
        # 使用新的充电决策模块进行慢充意愿评估
        from .charging_decision import ChargingWillingnessEvaluator
        evaluator = ChargingWillingnessEvaluator()
        return evaluator.evaluate_slow_charge_willingness(self, t, c, is_weekday)
    
    @property
    def trips(self) -> tuple[Trip, ...]:
        '''获取车辆的行程列表'''
        return self._trips

    @property
    def trips_count(self) -> int:
        '''获取车辆的行程数量'''
        return len(self._trips)

    @property
    def trip(self) -> Trip:
        '''获取当前行程'''
        return self._trips[self._trip_index]

    @property
    def trip_id(self) -> int:
        '''获取当前行程的ID（从0开始索引）'''
        return self._trip_index

    def next_trip(self) -> int:
        """
        将行程ID增加1并返回行程ID。如果已经是最后一个行程，返回-1。
        """
        if self._trip_index == len(self.trips) - 1:
            return -1
        self._trip_index += 1
        return self._trip_index

    @property
    def max_mileage(self) -> float:
        """
        当前电池电量下的最大行驶里程 (m)
        """
        return self._elec / self._consumption

    def is_batt_enough(self, dist: float) -> bool:
        """
        用户判断当前电池电量是否足以行驶dist距离
        """
        return self.max_mileage >= self._krel * dist

    def brief(self):
        """获取此车辆的简要描述"""
        return f"{self._id},{self.SOC*100:.1f}%,{self._trip_index}"

    def __repr__(self):
        return f"EV[ID='{self._id}', 状态={self._sta}, 距离={self._dis}m, SOC={self.SOC*100:.1f}%, 电池容量={self._bcap}kWh, 充电目标={self._chtar}kWh, 目标充电站={self._cs}, 能耗={self._consumption}KWh/m]"



    @property
    def is_private_car(self) -> bool:
        """判断是否为私家车"""
        return (self._vtype_category == "private" or
                (10 <= self._vtype_id <= 12))

    @property
    def is_taxi(self) -> bool:
        """判断是否为出租车"""
        return (self._vtype_category == "taxi" or
                (20 <= self._vtype_id <= 21))

    def get_charging_time_preference_weight(self, current_time_minutes: int, is_weekday: bool = True) -> float:
        """
        根据当前时间计算充电时间偏好权重
        参数:
            current_time_minutes: 当前时间（分钟，从0点开始计算）
            is_weekday: 是否为工作日
        返回:
            充电时间偏好权重（0-1之间）
        """
        # 使用新的充电决策模块进行时间偏好权重计算
        from .charging_decision import ChargingTimePreference

        # 确定车辆类型
        if self.is_taxi:
            vehicle_type = "taxi"
        else:
            vehicle_type = "private_car"

        time_preference = ChargingTimePreference(vehicle_type, is_weekday)
        return time_preference.get_preference_weight(current_time_minutes)

    def __str__(self):
        return repr(self)
