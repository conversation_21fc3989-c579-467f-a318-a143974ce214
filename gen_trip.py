from v2sim import TrafficGenerator

# 仿真案例配置
CASE_FOLDER = "cases/std_37nodes"          
VEHICLE_COUNT = 10000                      # 电动汽车数量
V2G_RATIO = 1.0                           # V2G参与比例 (0.0-1.0)
RANDOM_SEED = 12345                       # 随机种子，确保结果可重现
TRIP_PARAM_FOLDER = ""                    # 行程参数文件夹，空值使用默认

def main():
    """生成电动汽车行程数据"""
    # 直接调用EVTrips方法，不使用命令行参数
    from v2sim.trafficgen.core import DEFAULT_CNAME

    cname = TRIP_PARAM_FOLDER if TRIP_PARAM_FOLDER else DEFAULT_CNAME

    TrafficGenerator(CASE_FOLDER).EVTrips(
        n=VEHICLE_COUNT,
        seed=RANDOM_SEED,
        day_count=7,
        cname=cname,
        v2g_prop=V2G_RATIO
    )

if __name__ == "__main__":
    main()
