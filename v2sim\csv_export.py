#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CSV标准化导出模块
Standard CSV Export Module for V2SIM

功能：
- 将v2sim的压缩格式数据转换为标准CSV格式
- 支持实时导出和批量导出
- 兼容Windows系统路径和编码
"""

import os
import csv
import pandas as pd
from pathlib import Path
from typing import Dict, List, Optional, Any
from collections import defaultdict
import time

from .statistics.manager import StaReader




class CSVExportManager:
    """CSV标准化导出管理器"""
    
    def __init__(self, source_dir: str, output_dir: str, enabled: bool = True):
        """
        初始化CSV导出管理器
        
        Args:
            source_dir: v2sim原始结果目录
            output_dir: CSV导出目录
            enabled: 是否启用CSV导出
        """
        self.source_dir = Path(source_dir)
        self.output_dir = Path(output_dir) / "csv_exports"
        self.enabled = enabled
        
        if self.enabled:
            # 创建输出目录结构
            self.output_dir.mkdir(parents=True, exist_ok=True)
    
    def export_all(self) -> bool:
        """
        导出所有可用的数据为标准CSV格式
        
        Returns:
            bool: 导出是否成功
        """
        if not self.enabled:
            return True
            
        try:
            print("开始CSV标准化导出...")
            
            # 检查源目录是否存在
            if not self.source_dir.exists():
                print(f"错误: 源目录不存在 {self.source_dir}")
                return False
            
            # 导出各类数据
            success = True
            success &= self._export_vehicle_data()
            success &= self._export_charging_station_data()
            success &= self._export_power_grid_data()
            success &= self._export_summary_data()
            
            if success:
                print(f"CSV导出完成，文件保存在: {self.output_dir}")
                self._create_readme()
            else:
                print("CSV导出过程中出现错误")
                
            return success
            
        except Exception as e:
            print(f"CSV导出失败: {e}")
            return False
    
    def _export_vehicle_data(self) -> bool:
        """导出电动汽车数据"""
        try:
            # 导出EV数据
            if (self.source_dir / "ev.csv").exists():
                print("正在导出电动汽车数据...")
                ev_data = self._convert_compressed_csv("ev.csv")
                if ev_data is not None:
                    ev_data.to_csv(self.output_dir / "ev.csv",
                                 index=False, encoding='utf-8-sig')

            # 导出按车型分类的EV数据
            if (self.source_dir / "ev_by_type.csv").exists():
                print("正在导出按车型分类的电动汽车数据...")
                ev_type_data = self._convert_compressed_csv("ev_by_type.csv")
                if ev_type_data is not None:
                    ev_type_data.to_csv(self.output_dir / "ev_by_type.csv",
                                      index=False, encoding='utf-8-sig')

            return True

        except Exception as e:
            print(f"导出电动汽车数据失败: {e}")
            return False
    
    def _export_charging_station_data(self) -> bool:
        """导出充电站数据"""
        try:
            # 导出快充站数据
            if (self.source_dir / "fcs.csv").exists():
                print("正在导出快充站数据...")
                fcs_data = self._convert_compressed_csv("fcs.csv")
                if fcs_data is not None:
                    fcs_data.to_csv(self.output_dir / "fcs.csv",
                                  index=False, encoding='utf-8-sig')

            # 导出慢充站数据
            if (self.source_dir / "scs.csv").exists():
                print("正在导出慢充站数据...")
                scs_data = self._convert_compressed_csv("scs.csv")
                if scs_data is not None:
                    scs_data.to_csv(self.output_dir / "scs.csv",
                                  index=False, encoding='utf-8-sig')

            return True

        except Exception as e:
            print(f"导出充电站数据失败: {e}")
            return False
    
    def _export_power_grid_data(self) -> bool:
        """导出电网数据"""
        try:
            # 导出母线数据
            if (self.source_dir / "bus.csv").exists():
                print("正在导出电网母线数据...")
                bus_data = self._convert_compressed_csv("bus.csv")
                if bus_data is not None:
                    bus_data.to_csv(self.output_dir / "bus.csv",
                                  index=False, encoding='utf-8-sig')

            # 导出发电机数据
            if (self.source_dir / "gen.csv").exists():
                print("正在导出发电机数据...")
                gen_data = self._convert_compressed_csv("gen.csv")
                if gen_data is not None:
                    gen_data.to_csv(self.output_dir / "gen.csv",
                                  index=False, encoding='utf-8-sig')

            # 导出输电线路数据
            if (self.source_dir / "line.csv").exists():
                print("正在导出输电线路数据...")
                line_data = self._convert_compressed_csv("line.csv")
                if line_data is not None:
                    line_data.to_csv(self.output_dir / "line.csv",
                                   index=False, encoding='utf-8-sig')

            return True

        except Exception as e:
            print(f"导出电网数据失败: {e}")
            return False
    
    def _export_summary_data(self) -> bool:
        """导出汇总数据"""
        try:
            print("正在生成汇总数据...")
            
            # 创建系统概览数据
            summary_data = []
            
            # 统计各类数据文件信息
            for csv_file in self.source_dir.glob("*.csv"):
                if csv_file.name.startswith(('.', 'cproc')):
                    continue
                    
                file_info = {
                    'data_type': csv_file.stem,
                    'file_name': csv_file.name,
                    'file_size_mb': round(csv_file.stat().st_size / (1024*1024), 2),
                    'last_modified': time.strftime('%Y-%m-%d %H:%M:%S', 
                                                 time.localtime(csv_file.stat().st_mtime))
                }
                summary_data.append(file_info)
            
            if summary_data:
                summary_df = pd.DataFrame(summary_data)
                summary_df.to_csv(self.output_dir / "data_overview.csv",
                                index=False, encoding='utf-8-sig')
            
            return True
            
        except Exception as e:
            print(f"导出汇总数据失败: {e}")
            return False
    
    def _convert_compressed_csv(self, filename: str) -> Optional[pd.DataFrame]:
        """
        转换v2sim压缩格式CSV为标准时间序列格式
        格式：Time | 母线1 | 母线2 | ... | 线路1 | 线路2 | ...

        Args:
            filename: CSV文件名

        Returns:
            转换后的DataFrame，失败时返回None
        """
        try:
            file_path = self.source_dir / filename
            if not file_path.exists():
                return None

            with open(file_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()

            if len(lines) < 3:
                return None

            # 检查是否为压缩格式
            is_compressed = lines[0].strip() == 'C'

            if is_compressed:
                # 解析压缩格式的列名
                column_names = lines[1].strip().split(',')
                data_lines = lines[3:]  # 跳过"Time,Item,Value"行

                # 创建时间序列数据结构
                time_series_data = {}
                current_time = None

                for line in data_lines:
                    line = line.strip()
                    if not line:
                        continue

                    parts = line.split(',')
                    if len(parts) >= 3:
                        time_str, item_idx, value = parts[0], parts[1], parts[2]

                        if time_str:  # 新的时间点
                            current_time = int(time_str)

                        if current_time is not None:
                            # 初始化时间点数据
                            if current_time not in time_series_data:
                                time_series_data[current_time] = {}

                            # 获取列名 - 使用自定义解码
                            try:
                                item_idx_decoded = self._decode_item_index(item_idx)
                                if item_idx_decoded < len(column_names):
                                    column_name = column_names[item_idx_decoded]
                                    time_series_data[current_time][column_name] = float(value)
                            except (ValueError, IndexError):
                                # 解码失败则跳过这行数据
                                continue

                # 转换为DataFrame
                if time_series_data:
                    # 创建时间索引
                    times = sorted(time_series_data.keys())
                    all_columns = set()
                    for time_data in time_series_data.values():
                        all_columns.update(time_data.keys())
                    all_columns = sorted(all_columns)

                    # 构建数据矩阵，使用前向填充处理缺失值
                    data_matrix = []
                    last_values = {}  # 存储每列的最后一个值

                    for time_point in times:
                        row = [time_point]  # 第一列是时间
                        for col in all_columns:
                            if col in time_series_data[time_point]:
                                # 有新值，更新并使用
                                value = time_series_data[time_point][col]
                                last_values[col] = value
                            else:
                                # 没有新值，使用前一个值
                                value = last_values.get(col, None)
                            row.append(value)
                        data_matrix.append(row)

                    # 创建DataFrame
                    columns = ['Time'] + all_columns
                    df = pd.DataFrame(data_matrix, columns=columns)

                    # 添加时间格式列
                    df['Time_hours'] = df['Time'] / 3600

                    # 重新排列列顺序：Time, Time_hours, 然后是其他列
                    other_cols = [col for col in df.columns if col not in ['Time', 'Time_hours']]
                    df = df[['Time', 'Time_hours'] + other_cols]

                    return df
                else:
                    return None
            else:
                # 标准格式，直接读取
                return pd.read_csv(file_path)

        except Exception as e:
            print(f"转换文件 {filename} 失败: {e}")
            return None

    def _decode_item_index(self, item_str: str) -> int:
        """
        解码项目索引 - 最终修正版
        编码规则：
        - 0-9: 索引 0-9 (单位数字)
        - A-Z: 索引 10-35 (大写字母)
        - a-z: 索引 36-61 (小写字母)
        - 41-91: 索引 66-71 (数字4-9 + '1')
        - A1-Z1: 索引 72-97 (字母A-Z + '1')
        - a1-z1: 索引 98-123 (小写字母a-z + '1')
        """
        if len(item_str) == 1 and item_str.isdigit():
            # 单位数字：0-9
            return int(item_str)
        elif len(item_str) == 1 and item_str.isupper():
            # 大写字母：A-Z
            return ord(item_str) - ord('A') + 10
        elif len(item_str) == 1 and item_str.islower():
            # 小写字母：a-z
            return ord(item_str) - ord('a') + 36
        elif len(item_str) == 2 and item_str[1] == '1':
            # 带1后缀的编码 - 优先检查
            if item_str[0].isdigit():
                # 数字+1：41, 51, 61, 71, 81, 91 -> 索引 66-71
                return int(item_str[0]) + 62
            elif item_str[0].isupper():
                # 大写字母+1：A1, B1, ..., Z1 -> 索引 72-97
                return ord(item_str[0]) - ord('A') + 72
            elif item_str[0].islower():
                # 小写字母+1：a1, b1, ..., z1 -> 索引 98-123
                return ord(item_str[0]) - ord('a') + 98
        elif len(item_str) == 2 and item_str.isdigit():
            # 两位数字：其他情况
            num = int(item_str)
            if 10 <= num <= 99:
                # 这些是直接的索引值
                return num

        # 无法解码，抛出异常
        raise ValueError(f"无法解码项目索引: {item_str}")
    

    
    def _create_readme(self):
        """创建README说明文件"""
        readme_content = """# V2SIM CSV导出数据说明

## 文件列表

### 电动汽车数据
- `ev.csv`: 电动汽车时间序列数据（包含SOC、状态、成本等）
- `ev_by_type.csv`: 按车型分类的电动汽车数据

### 充电站数据
- `fcs.csv`: 快充站运行时间序列数据
- `scs.csv`: 慢充站运行时间序列数据

### 电网数据
- `bus.csv`: 电网母线时间序列数据（电压、负荷等）
- `gen.csv`: 发电机运行时间序列数据
- `line.csv`: 输电线路时间序列数据

### 汇总数据
- `data_overview.csv`: 数据文件概览信息

## 数据格式说明

### 标准时间序列格式
所有CSV文件采用统一的时间序列格式：
```
Time | Time_hours | 设备1 | 设备2 | 设备3 | ...
0    | 0.0        | 值1   | 值2   | 值3   | ...
10   | 0.0028     | 值1   | 值2   | 值3   | ...
20   | 0.0056     | 值1   | 值2   | 值3   | ...
```

### 时间格式
- `Time`: 仿真时间（秒）
- `Time_hours`: 仿真时间（小时）

### 列名格式
- **电动汽车**: v0#soc, v0#sta, v1#soc, v1#sta... (车辆编号#属性)
- **充电站**: fcs0#power, fcs0#count, scs0#power... (站点编号#属性)
- **母线**: b1#voltage, b1#load, b2#voltage... (母线编号#属性)
- **线路**: l1#power, l1#current, l2#power... (线路编号#属性)
- **发电机**: g1#power, g1#voltage, g2#power... (发电机编号#属性)

### 数据单位
- **电压**: 标幺值(p.u.)或千伏(kV)
- **功率**: 兆瓦(MW)或千瓦(kW)
- **电流**: 安培(A)或千安(kA)
- **SOC**: 0-1之间的小数
- **时间**: 秒(s)或小时(h)

## 使用建议

1. **Excel打开**: 文件使用UTF-8-BOM编码，Excel可直接打开显示中文
2. **数据分析**: 推荐使用Python pandas或R进行大数据分析
3. **可视化**: 时间序列格式便于绘制趋势图和对比分析
4. **筛选数据**: 可根据列名筛选特定设备或属性的数据

## 示例用法

### Python读取示例
```python
import pandas as pd

# 读取母线数据
bus_data = pd.read_csv('power_grid/bus_timeseries.csv')

# 绘制母线1电压曲线
import matplotlib.pyplot as plt
plt.plot(bus_data['Time_hours'], bus_data['b1#voltage'])
plt.xlabel('时间(小时)')
plt.ylabel('电压(p.u.)')
plt.title('母线1电压时间序列')
plt.show()
```

生成时间：{timestamp}
"""
        
        readme_path = self.output_dir / "README.md"
        with open(readme_path, 'w', encoding='utf-8') as f:
            f.write(readme_content.format(timestamp=time.strftime('%Y-%m-%d %H:%M:%S')))
