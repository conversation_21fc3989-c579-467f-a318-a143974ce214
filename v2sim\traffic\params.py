"""
交通系统参数配置文件
定义系统默认参数和行为控制开关
"""

# 关闭找不到路径的错误提示
SUPPRESS_ROUTE_NOT_FOUND = True

# 默认快充站的充电桩数量, 如果*.fcs.xml中定义了快充站参数，则不会使用此项
DEFAULT_CS_SLOTS = 20

# 默认路边慢充的充电桩数量, 如果*.scs.xml中定义了路边慢充参数，则不会使用此项
DEFAULT_PK_SLOTS = 20

# 默认购电价格, 如果*.fcs.xml或*.scs.xml中定义了购电价格，则对应的快充站或路边慢充不会使用此项
DEFAULT_BUY_PRICE = ([0], [1.5])

# 默认售电价格, 如果*.fcs.xml或*.scs.xml中定义了购电价格，则对应的快充站或路边慢充不会使用此项
DEFAULT_SELL_PRICE = ([0], [1.0])

# 充电效率和放电效率（系统级参数，不会被车辆生成覆盖）
DEFAULT_ETA_CHARGE = 0.9
DEFAULT_ETA_DISCHARGE = 0.9

# 指示根据电量是否满足行程长度来充电
ENABLE_DIST_BASED_CHARGING_DECISION = False

# 指示在快充站是否根据行程长度确定充电量, True表示充电量刚好够行驶到终点(会稍微多充一点, 由Krel控制), False表示总是充满
ENABLE_DIST_BASED_CHARGING_QUANTITY = False

# 指示是否允许CSList中同时存在快充站和慢充站
ALLOW_MIXED_CSTYPE_IN_CSLIST = False

# 默认充电站所属母线名称
DEFAULT_BUS_NAME = "BusAny"

# 默认电动车充电功率修正函数
DEFAULT_RMOD = "Linear"

# 默认最大慢充费用（$/kWh）
DEFAULT_MAX_SC_COST = 100.0

# 默认最小V2G收益（$/kWh）
DEFAULT_MIN_V2G_EARN = 0.0

# 道路距离缩放因子 (1.0 = 原始距离, 2.0 = 距离翻倍, 0.5 = 距离减半)
ROAD_DISTANCE_SCALE_FACTOR = 1.2
