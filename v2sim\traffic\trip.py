"""
行程记录模块
记录车辆行程和充电事件的日志系统
"""
from typing import Literal, Optional

from .utils import TWeights
from .ev import EV


class TripsLogger:
    """行程日志记录器"""
    ARRIVAL_NO_CHARGE = 0           # 到达时无需充电
    ARRIVAL_CHARGE_SUCCESSFULLY = 1  # 到达时充电成功
    ARRIVAL_CHARGE_FAILED = 2       # 到达时充电失败

    def __init__(self, file_name: str):
        self.__ostream = open(file_name, 'w', encoding='utf-8')

    def __pr(self, *args):
        """内部打印方法"""
        print(*args, file=self.__ostream, sep="|")

    def arrive(self, simT: int, veh: EV, status: Literal[0, 1, 2]): 
        tid = veh.trip_id
        if tid < veh.trips_count - 1:
            nt = veh.trips[tid + 1]
        else:
            nt = None
        self.__pr(simT, 'A', veh.brief(), status, veh.trip.arrive_edge, nt)

    def arrive_CS(self, simT: int, veh: EV, cs: str):
        self.__pr(simT, 'AC', veh.brief(), cs)

    def depart(self, simT: int, veh: EV, delay:int = 0, cs: Optional[str] = None, cs_param:Optional[TWeights] = None):
        if cs_param:
            cs_param_str = f'{cs_param[0]:.3f},{cs_param[1]:.3f},{cs_param[2]:.3f}'
        else:
            cs_param_str = ''
        self.__pr(simT, 'D', veh.brief(), veh.trip, delay, cs, cs_param_str)

    def depart_delay(self, simT: int, veh: EV, batt_req: float, delay:int):
        self.__pr(simT, 'DD', veh.brief(), veh.battery, batt_req, delay)
    
    def depart_CS(self, simT: int, veh: EV, cs: str):
        self.__pr(simT, 'DC', veh.brief(), cs, veh.trip.arrive_edge)
    
    def depart_failed(self, simT: int, veh: EV, batt_req: float, cs: str, trT:int):
        self.__pr(simT, 'DF', veh.brief(), veh.battery, batt_req, cs, trT)
    
    def fault_deplete(self, simT: int, veh: EV, cs: str, trT:int):
        self.__pr(simT, 'FD', veh.brief(), cs, trT)
    
    def fault_nocharge(self, simT: int, veh: EV, cs: str):
        self.__pr(simT, 'FN', veh.brief(), veh.battery, cs)
    
    def fault_redirect(self, simT: int, veh: EV, cs_old: str, cs_new: str):
        self.__pr(simT, 'FR', veh.brief(), veh.battery, cs_old, cs_new)

    def warn_smallcap(self, simT: int, veh: EV, batt_req: float):
        self.__pr(simT, 'WC', veh.brief(), veh.battery, batt_req)
    
    def close(self):
        self.__ostream.close()

class TripLogItem:
    OP_NAMEs = {
        'A': "到达",
        'AC': "到达充电站",
        'D': "出发",
        'DD': "延迟出发",
        'DC': "从充电站出发",
        'DF': "出发失败",
        'FD': "电量耗尽故障",
        'WC': "电池容量不足警告"
    }
    def __init__(self, simT:int, op:str, veh_id:str, veh_soc:str, trip_id:int, additional:dict[str,str]):
        self.simT = simT
        self.__op = op
        self.veh = veh_id
        self.veh_soc = veh_soc
        self.trip_id = trip_id
        self.additional = additional

    def to_tuple(self,conv:bool=False):
        op = self.__op if not conv else TripLogItem.OP_NAMEs[self.__op]
        return (self.simT, op, self.veh, self.veh_soc, self.trip_id, self.additional.get('cs_param',''), self.additional)
    
    @property
    def op_raw(self):
        return self.__op

    @property
    def op(self):
        return TripLogItem.OP_NAMEs[self.__op]
    
    @property
    def cs_param(self):
        return self.additional.get('cs_param', None)
    
    def __repr__(self):
        return f"{self.simT}|{self.__op}|{self.veh},{self.veh_soc},{self.trip_id}|{self.additional}"
    
    def __str__(self):
        ret = f"[{self.simT},{TripLogItem.OP_NAMEs[self.__op]}]"
        veh = f"{self.veh}(Soc={self.veh_soc},TripID={self.trip_id})"
        if self.__op == 'A':
            if self.additional['status'] == '0':
                pos2 = "无需充电"
            elif self.additional['status'] == '1':
                pos2 = "充电成功"
            elif self.additional['status'] == '2':
                pos2 = "充电失败"
            ret += f"车辆{veh}到达{self.additional['arrive_edge']}, {pos2}, 下一行程: {self.additional['next_trip']}"
        elif self.__op == 'AC':
            ret += f"车辆{veh}到达充电站{self.additional['cs']}"
        elif self.__op == 'D':
            ret += f"车辆{veh}开始行程{self.additional['trip']}"
            if int(self.additional['delay']) <= 0:
                ret += f", 行程{self.additional['trip']}延迟出发"
            if self.additional['cs'] != "None":
                ret += f", 目标充电站: {self.additional['cs']}, 参数: {self.additional['cs_param']}"
        elif self.__op == 'DD':
            ret += f"车辆{veh}延迟出发, 当前电量: {self.additional['veh_batt']}, 需要电量: {self.additional['batt_req']}, 延迟时间: {self.additional['delay']}"
        elif self.__op == 'DC':
            ret += f"车辆{veh}从充电站{self.additional['cs']}出发, 目的地: {self.additional['arrive_edge']}"
        elif self.__op == 'DF':
            ret += f"车辆{veh}出发失败, 当前电量: {self.additional['veh_batt']}, 需要电量: {self.additional['batt_req']}, 目标充电站: {self.additional['cs']}, 传输时间: {self.additional['trT']}"
        elif self.__op == 'F':
            ret += f"车辆{veh}电量耗尽故障, 目标充电站: {self.additional['cs']}, 传输时间: {self.additional['trT']}"
        elif self.__op == 'W':
            ret += f"车辆{veh}电池容量不足警告, 当前电量: {self.additional['veh_batt']}, 需要电量: {self.additional['batt_req']}"
        else:
            raise ValueError(f"Unknown operation {self.__op}")
        return ret
    
class TripsReader:
    def __init__(self, filename:str):
        with open(filename, 'r', encoding='utf-8') as fp:
            self.raw_texts = fp.readlines()
        self.meta_data:list[TripLogItem] = []
        self.translated_texts:list[str] = []
        for d in map(lambda x: x.strip().split('|'), self.raw_texts):
            simT = int(d[0])
            op = d[1]
            veh, soc, tripid = d[2].split(',')
            additional:dict[str,str] = {}
            if op == 'A':
                assert len(d) == 6
                additional['status'] = d[3]
                additional['arrive_edge'] = d[4]
                additional['next_trip'] = d[5]
            elif op == 'AC':
                assert len(d) == 4
                additional['cs'] = d[3]
            elif op == 'D':
                assert len(d) == 7
                additional['trip'] = d[3]
                additional['delay'] = d[4]
                additional['cs'] = d[5]
                additional['cs_param'] = d[6]
            elif op == 'DD':
                assert len(d) == 6
                additional['veh_batt'] = d[3]
                additional['batt_req'] = d[4]
                additional['delay'] = d[5]
            elif op == 'DC':
                assert len(d) == 5
                additional['cs'] = d[3]
                additional['arrive_edge'] = d[4]
            elif op == 'DF':
                assert len(d) == 7
                additional['veh_batt'] = d[3]
                additional['batt_req'] = d[4]
                additional['cs'] = d[5]
                additional['trT'] = d[6]
            elif op == 'F':
                assert len(d) == 5
                additional['cs'] = d[3]
                additional['trT'] = d[4]
            elif op == 'W':
                assert len(d) == 5
                additional['veh_batt'] = d[3]
                additional['batt_req'] = d[4]
            else:
                raise ValueError(f"Unknown operation {op}")
            met = TripLogItem(simT, op, veh, soc, int(tripid), additional)
            self.meta_data.append(met)
            self.translated_texts.append(str(met))
            
    def __iter__(self):
        return iter(self.translated_texts)
    
    def __len__(self):
        return len(self.translated_texts)
    
    def filter(self, 
        time:Optional[tuple[Optional[int],Optional[int]]]=None, 
        action:Optional[list[str]]=None, 
        veh:Optional[str]=None, 
        trip_id:Optional[int]=None):
        for r,m,t in zip(self.raw_texts, self.meta_data, self.translated_texts):
            if time is not None:
                if time[0] is not None:
                    if not time[0] <= m.simT:
                        continue
                if time[1] is not None:
                    if not m.simT <= time[1]:
                        continue
            if action is not None:
                if m.op_raw not in action:
                    continue
            if veh is not None:
                if m.veh != veh:
                    continue
            if trip_id is not None:
                if m.trip_id != trip_id:
                    continue
            yield r, m, t