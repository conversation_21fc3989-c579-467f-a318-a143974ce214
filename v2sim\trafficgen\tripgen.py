
import random, time, sumolib
from typing import Dict, List, Optional, Union
from feasytools import ReadOnlyTable, CDDiscrete, PDDiscrete, PDNormal, DTypeEnum

from ..traffic import EV, EVDict, ReadXML, DetectFiles
from .misc import VehicleType, random_diff, _TripInner, _EVInner, _xmlSaver

DictPDF = dict[int, Union[PDDiscrete[int], None]]

TAZ_TYPE_LIST = ("Home", "Work", "Relax", "Other")

class EVsGenerator:
    """电动车行程生成器"""
    def __init__(self, CROOT: str, PNAME: str, seed,
            is_weekday: bool = True):
        """
        初始化电动车行程生成器
            CROOT: 行程参数文件夹路径
            PNAME: SUMO配置文件夹路径
            seed: 随机种子
            is_weekday: 工作日模式标志（True=工作日，False=周末）
        """
        _fn = DetectFiles(PNAME)
        random.seed(seed)
        # 读取车辆类型配置，正确处理字符串字段
        import pandas as pd
        df = pd.read_csv(CROOT + "/ev_types.csv")
        self.vTypes = []
        for _, row in df.iterrows():
            vt = VehicleType(
                id=int(row['id']),
                bcap_kWh=float(row['bcap_kWh']),
                range_km=float(row['range_km']),
                efc_rate_kW=float(row['efc_rate_kW']),
                esc_rate_kW=float(row['esc_rate_kW']),
                max_V2G_kW=float(row['max_V2G_kW']),
                category=str(row['category'])
            )
            self.vTypes.append(vt)

        # 加载车辆类别比例配置
        self.__load_category_ratio(CROOT)

        # 工作日/周末模式配置
        self.is_weekday = is_weekday

        # Define various functional area types
        self.dic_taz = {}
        self.net:sumolib.net.Net = sumolib.net.readNet(_fn["net"])
        assert _fn.taz and _fn.taz_type, "错误: 未指定TAZ文件或TAZ类型文件"
        self.dic_taztype = {}
        with open(_fn.taz_type, "r") as fp:
            for ln in fp.readlines():
                name, lst = ln.split(":")
                self.dic_taztype[name.strip()] = [x.strip() for x in lst.split(",")]
        root = ReadXML(_fn.taz).getroot()
        if root is None: raise RuntimeError("错误: 未指定TAZ文件或TAZ类型文件")
        for taz in root.findall("taz"):
            taz_id = taz.attrib["id"]
            if "edges" in taz.attrib:
                self.dic_taz[taz_id] = taz.attrib["edges"].split(" ")
            else:
                self.dic_taz[taz_id] = [edge.attrib["id"] for edge in taz.findall("tazSource")]
        

        # Spatial transfer probability of weekday and weekend. 
        # key1 = from_type, key2 = time (0~95, each unit = 15min), value = CDF of (to_type1, to_type2, to_type3, to_type4)
        self.PSweekday:dict[str, DictPDF] = {}
        self.PSweekend:dict[str, DictPDF] = {}
        # Parking duration CDF of weekday and weekend.
        self.park_cdf_wd:dict[str, CDDiscrete[int]] = {} 
        self.park_cdf_we:dict[str, CDDiscrete[int]] = {}

        def read_trans_pdfs(path:str) -> DictPDF:
            tbwd = ReadOnlyTable(path, dtype=DTypeEnum.FLOAT32)
            times = [int(x) for x in tbwd.head[1:]]
            values = list(map(int, tbwd.col(0)))
            ret:DictPDF = {}
            for i in range(1, len(times)+1):
                weights = list(map(float, tbwd.col(i)))
                assert len(values) == len(weights)
                try:
                    ret[i] = PDDiscrete(values, weights)
                except ZeroDivisionError:
                    ret[i] = None
            return ret
        
        for dtype in TAZ_TYPE_LIST:
            self.PSweekday[dtype] = read_trans_pdfs(f"{CROOT}/space_transfer_probability/{dtype[0]}_spr_weekday.csv")
            self.PSweekend[dtype] = read_trans_pdfs(f"{CROOT}/space_transfer_probability/{dtype[0]}_spr_weekend.csv")
            self.park_cdf_wd[dtype] = CDDiscrete(f"{CROOT}/duration_of_parking/{dtype[0]}_spr_weekday.csv", True, int)
            self.park_cdf_we[dtype] = CDDiscrete(f"{CROOT}/duration_of_parking/{dtype[0]}_spr_weekend.csv", True, int)

        # 正态分布SOC初始化 - 基于公式(16): f(E₀) = 1/(σ√2π) * exp[-(E₀-μ)²/(2σ²)]
        # 其中: μ = 0.6, σ = 0.1, 0 < E₀ < 1
        self.soc_pdf = PDNormal(0.6, 0.1)  # 均值0.6，标准差0.1

        # 读取车辆类别比例配置
        self.__load_category_ratio(CROOT)

        # 初始化差异化出行模式参数
        self.__init_private_car_params()
        self.__init_taxi_params()

    def __is_weekday(self, daynum: int) -> bool:
        """
        判断指定天数是否为工作日
        Args:
            daynum: 天数，0为初始化天，1开始为正式仿真天
        Returns:
            True: 工作日模式, False: 周末模式
        """
        if daynum == 0:
            return True  # 第0天始终为初始化，按工作日处理
        else:
            return self.is_weekday  # 使用配置参数



    def __load_category_ratio(self, CROOT: str):
        """加载车辆类别比例配置"""
        import pandas as pd
        import os

        ratio_file = os.path.join(CROOT, "vehicle_ratio.csv")

        # 读取比例配置文件
        df_ratio = pd.read_csv(ratio_file)
        self.category_weights = {}

        for _, row in df_ratio.iterrows():
            category = str(row['category'])
            weight = float(row['weight'])
            self.category_weights[category] = weight

        print(f"已加载车辆类别比例配置: {self.category_weights}")

    def __select_vehicle_category_by_weight(self):
        """根据权重选择车辆类别"""
        import random

        categories = list(self.category_weights.keys())
        weights = list(self.category_weights.values())

        # 根据权重随机选择类别
        selected_category = random.choices(categories, weights=weights, k=1)[0]

        # 从该类别中随机选择一个车型
        available_types = [vt for vt in self.vTypes if vt.category == selected_category]

        if available_types:
            return random.choice(available_types)
        else:
            # 如果该类别没有车型，回退到随机选择
            return random.choice(self.vTypes)

    def __init_private_car_params(self):
        """初始化私家车出行参数"""
        from feasytools.pdf import PDNormal

        # 工作日出行链比例（基于公式1）
        self.private_trip_chains_weekday = {
            "H-W-H": 0.5597,                # 家-工作-家
            "H-W-SR/SE/O-H": 0.2034,        # 家-工作-休闲/购物/其他-家
            "H-SR/SE/O-H": 0.2369           # 家-休闲/购物/其他-家
        }

        # 周末出行链比例
        self.private_trip_chains_weekend = {
            "H-SR/SE/O-H_AM": 0.35,         # 家-休闲/购物/其他-家（上午）
            "H-SR/SE/O-H_PM": 0.35,         # 家-休闲/购物/其他-家（下午）
            "NO_TRIP": 0.30                 # 无出行
        }

        # 时间分布参数（正态分布）
        self.private_depart_home_weekday = PDNormal(6.92, 1.24)      # 工作日从家出发
        self.private_depart_work = PDNormal(17.47, 1.8)             # 从公司出发
        self.private_stay_weekday = PDNormal(2, 0.5)                # 工作日停留时间
        self.private_depart_home_weekend_am = PDNormal(8.98, 3.24)  # 周末从家出发(上午)
        self.private_depart_home_weekend_pm = PDNormal(16.47, 3.41) # 周末从家出发(下午)
        self.private_stay_weekend = PDNormal(6, 3)                  # 周末停留时间

    def __init_taxi_params(self):
        """初始化出租车出行参数"""
        # 双峰分布参数（基于公式2）
        self.taxi_lambda1, self.taxi_alpha1, self.taxi_beta1 = 0.389, 7.046, 1.086
        self.taxi_lambda2, self.taxi_alpha2, self.taxi_beta2 = 0.066, 15.610, 9.667

        # 出租车运营特征
        self.taxi_daily_trips = 15          # 每日平均行程数
        self.taxi_service_hours = (6, 22)   # 服务时间段 6:00-22:00
        self.taxi_break_duration = 2        # 休息时长（小时）



    def __getPs(self, is_weekday: bool, dtype: str, time_index:int):
        return self.PSweekday[dtype].get(time_index, None) if is_weekday else self.PSweekend[dtype].get(time_index, None)
    




    def __getNextTAZandPlace(self, from_TAZ:str, from_EDGE:str, next_place_type:str) -> tuple[str,str,list[str]]:
        trial = 0
        while True:
            to_TAZ = random.choice(self.dic_taztype[next_place_type])
            to_EDGE = random_diff(self.dic_taz[to_TAZ], from_EDGE)
            if from_EDGE != to_EDGE:
                route = [from_EDGE, to_EDGE]
                return to_TAZ, to_EDGE, route
            trial += 1
            if trial >= 5:
                raise RuntimeError("from_EDGE == to_EDGE")
        
    


    cdf_dict = {}

    def __genStopTimeIdx(self, from_type:str, weekday: bool):
        cdf = self.park_cdf_wd[from_type] if weekday else self.park_cdf_we[from_type]
        return int(cdf.sample() + 1)




        
    def __genEV(self, veh_id: str, day_count: int, **kwargs) -> _EVInner:
        """
        生成一辆车的完整行程（内部实例）
            veh_id: 车辆ID
            day_count: 生成天数
            **kwargs: 其他参数，可包含vehicle_type_id或vehicle_category
        """
        # 选择车型
        if "vehicle_type_id" in kwargs:
            # 指定车型ID
            type_id = kwargs.pop("vehicle_type_id")
            selected_type = next((vt for vt in self.vTypes if vt.id == type_id), None)
            if selected_type is None:
                selected_type = self.__select_vehicle_category_by_weight()
        elif "vehicle_category" in kwargs:
            # 指定车辆类别
            category = kwargs.pop("vehicle_category")
            available_types = [vt for vt in self.vTypes if vt.category == category]
            selected_type = random.choice(available_types) if available_types else self.__select_vehicle_category_by_weight()
        else:
            # 根据类别权重选择
            selected_type = self.__select_vehicle_category_by_weight()

        # 采样初始SOC并确保在合理范围内[0.1, 1.0]
        soc_sample = max(0.1, min(1.0, self.soc_pdf.sample()))

        ev = _EVInner(veh_id, selected_type, soc_sample, **kwargs)

        # 根据车型选择出行模式
        if selected_type.is_private_car:
            self.__genTripsChain_Private(ev)
            for j in range(1, day_count + 1):
                self.__genTripsChainA_Private(ev, j)
        elif selected_type.is_taxi:
            self.__genTripsChain_Taxi(ev)
            for j in range(1, day_count + 1):
                self.__genTripsChainA_Taxi(ev, j)
        else:
            # 如果车型不匹配，默认使用私家车模式
            print(f"Warning: Unknown vehicle type {selected_type.id}, using private car mode")
            self.__genTripsChain_Private(ev)
            for j in range(1, day_count + 1):
                self.__genTripsChainA_Private(ev, j)

        return ev

    def __select_private_trip_chain(self, weekday: bool) -> str:
        """选择私家车出行链类型"""
        chains = self.private_trip_chains_weekday if weekday else self.private_trip_chains_weekend
        rand = random.random()
        cumulative = 0
        for chain, prob in chains.items():
            cumulative += prob
            if rand <= cumulative:
                return chain
        return list(chains.keys())[-1]

    def __sample_taxi_departure_time(self) -> float:
        """采样出租车出发时间（双峰分布）"""
        import math

        # 使用接受-拒绝采样法实现双峰分布
        max_attempts = 1000
        for _ in range(max_attempts):
            # 在服务时间范围内随机采样
            t = random.uniform(self.taxi_service_hours[0], self.taxi_service_hours[1])

            # 计算概率密度 f(t) = λ1*exp(-((t-α1)/β1)²) + λ2*exp(-((t-α2)/β2)²)
            term1 = self.taxi_lambda1 * math.exp(-((t - self.taxi_alpha1) / self.taxi_beta1) ** 2)
            term2 = self.taxi_lambda2 * math.exp(-((t - self.taxi_alpha2) / self.taxi_beta2) ** 2)
            density = term1 + term2

            # 接受-拒绝判断
            if random.random() < density / 0.5:  # 0.5是近似的最大密度值
                return t

        # 如果采样失败，返回早高峰时间
        return 7.5



    def genEV(self, veh_id: str, day_count: int = 1, **kwargs) -> EV:
        """
        生成一辆车的完整行程
        生成的车辆作为EV实例返回，不会保存在缓冲区中
            veh_id: 车辆ID
            day_count: 生成天数，默认1天
            v2g_prop: 用户参与V2G的比例
            omega: PDFunc | None = None,
            krel: PDFunc | None = None,
            ksc: PDFunc | None = None,
            kfc: PDFunc | None = None,
            kv2g: PDFunc | None = None
        """
        return self.__genEV(veh_id, day_count, **kwargs).toEV()

    def __genTripsChain_Private(self, ev: _EVInner):
        """生成私家车第一天出行链"""
        daynum = 0
        weekday = True

        # 选择出行链类型
        chain_type = self.__select_private_trip_chain(weekday)

        if chain_type == "H-W-H":
            # 家-工作-家
            trip_1 = self.__genPrivateTrip_HomeToWork("trip0_1", weekday)
            trip_2 = self.__genPrivateTrip_WorkToHome("trip0_2", trip_1, weekday)
            ev._add_trip(daynum, trip_1)
            ev._add_trip(daynum, trip_2)

        elif chain_type == "H-W-SR/SE/O-H":
            # 家-工作-休闲/购物/其他-家
            trip_1 = self.__genPrivateTrip_HomeToWork("trip0_1", weekday)
            trip_2 = self.__genPrivateTrip_WorkToLeisure("trip0_2", trip_1, weekday)
            trip_3 = self.__genPrivateTrip_LeisureToHome("trip0_3", trip_2, weekday)
            ev._add_trip(daynum, trip_1)
            ev._add_trip(daynum, trip_2)
            if trip_3.DPTT < 86400:
                ev._add_trip(daynum, trip_3)

        elif chain_type == "H-SR/SE/O-H":
            # 家-休闲/购物/其他-家
            trip_1 = self.__genPrivateTrip_HomeToLeisure("trip0_1", weekday)
            trip_2 = self.__genPrivateTrip_LeisureToHome("trip0_2", trip_1, weekday)
            ev._add_trip(daynum, trip_1)
            if trip_2.DPTT < 86400:
                ev._add_trip(daynum, trip_2)

    def __genPrivateTrip_HomeToWork(self, trip_id: str, weekday: bool) -> _TripInner:
        """生成私家车家到工作地的行程"""
        from_TAZ = random.choice(self.dic_taztype["Home"])
        from_EDGE = random.choice(self.dic_taz[from_TAZ])

        # 使用正态分布采样出发时间
        depart_time_hour = max(0, min(24, self.private_depart_home_weekday.sample()))
        depart_time_min = int(depart_time_hour * 60)

        to_TAZ, to_EDGE, route = self.__getNextTAZandPlace(from_TAZ, from_EDGE, "Work")
        return _TripInner(trip_id, depart_time_min * 60, from_TAZ, from_EDGE,
                         to_TAZ, to_EDGE, route, "Work")

    def __genPrivateTrip_WorkToHome(self, trip_id: str, prev_trip: _TripInner, weekday: bool) -> _TripInner:
        """生成私家车工作地到家的行程"""
        from_TAZ = prev_trip.toTAZ
        from_EDGE = prev_trip.route[-1]

        # 使用正态分布采样出发时间
        depart_time_hour = max(0, min(24, self.private_depart_work.sample()))
        depart_time_min = int(depart_time_hour * 60)

        to_TAZ, to_EDGE, route = self.__getNextTAZandPlace(from_TAZ, from_EDGE, "Home")
        return _TripInner(trip_id, depart_time_min * 60, from_TAZ, from_EDGE,
                         to_TAZ, to_EDGE, route, "Home")

    def __genPrivateTrip_WorkToLeisure(self, trip_id: str, prev_trip: _TripInner, weekday: bool) -> _TripInner:
        """生成私家车工作地到休闲场所的行程"""
        from_TAZ = prev_trip.toTAZ
        from_EDGE = prev_trip.route[-1]

        # 下班后去休闲场所，时间在下班时间基础上加停留时间
        base_time = prev_trip.DPTT / 3600  # 转换为小时
        stay_hours = max(0, self.private_stay_weekday.sample())
        depart_time_hour = base_time + stay_hours
        depart_time_min = int(depart_time_hour * 60)

        # 选择休闲场所类型
        leisure_types = ["Relax", "Other"]
        dest_type = random.choice(leisure_types)

        to_TAZ, to_EDGE, route = self.__getNextTAZandPlace(from_TAZ, from_EDGE, dest_type)
        return _TripInner(trip_id, depart_time_min * 60, from_TAZ, from_EDGE,
                         to_TAZ, to_EDGE, route, dest_type)

    def __genPrivateTrip_LeisureToHome(self, trip_id: str, prev_trip: _TripInner, weekday: bool) -> _TripInner:
        """生成私家车休闲场所到家的行程"""
        from_TAZ = prev_trip.toTAZ
        from_EDGE = prev_trip.route[-1]

        # 在休闲场所停留后回家
        base_time = prev_trip.DPTT / 3600
        stay_hours = max(0, self.private_stay_weekday.sample() if weekday else self.private_stay_weekend.sample())
        depart_time_hour = base_time + stay_hours
        depart_time_min = int(depart_time_hour * 60)

        to_TAZ, to_EDGE, route = self.__getNextTAZandPlace(from_TAZ, from_EDGE, "Home")
        return _TripInner(trip_id, depart_time_min * 60, from_TAZ, from_EDGE,
                         to_TAZ, to_EDGE, route, "Home")

    def __genPrivateTrip_HomeToLeisure(self, trip_id: str, weekday: bool) -> _TripInner:
        """生成私家车家到休闲场所的行程"""
        from_TAZ = random.choice(self.dic_taztype["Home"])
        from_EDGE = random.choice(self.dic_taz[from_TAZ])

        # 根据是否工作日选择出发时间
        if weekday:
            depart_time_hour = max(0, min(24, self.private_depart_home_weekday.sample()))
        else:
            # 周末随机选择上午或下午出发
            if random.random() < 0.5:
                depart_time_hour = max(0, min(24, self.private_depart_home_weekend_am.sample()))
            else:
                depart_time_hour = max(0, min(24, self.private_depart_home_weekend_pm.sample()))

        depart_time_min = int(depart_time_hour * 60)

        # 选择休闲场所类型
        leisure_types = ["Relax", "Other"]
        dest_type = random.choice(leisure_types)

        to_TAZ, to_EDGE, route = self.__getNextTAZandPlace(from_TAZ, from_EDGE, dest_type)
        return _TripInner(trip_id, depart_time_min * 60, from_TAZ, from_EDGE,
                         to_TAZ, to_EDGE, route, dest_type)

    def __genTripsChainA_Private(self, ev: _EVInner, daynum: int):
        """生成私家车后续天出行链"""
        weekday = self.__is_weekday(daynum)

        # 从前一天最后位置开始
        last_trip = ev.trips[-1]
        current_taz = last_trip.toTAZ
        current_edge = last_trip.route[-1]

        # 选择出行链类型
        chain_type = self.__select_private_trip_chain(weekday)

        if chain_type == "NO_TRIP":
            # 周末无出行
            return

        # 类似第一天的逻辑，但从当前位置开始
        if chain_type == "H-W-H" and weekday:
            trip_1 = self.__genPrivateTrip_CurrentToWork(f"trip{daynum}_1", current_taz, current_edge, weekday)
            trip_2 = self.__genPrivateTrip_WorkToHome(f"trip{daynum}_2", trip_1, weekday)
            ev._add_trip(daynum, trip_1)
            ev._add_trip(daynum, trip_2)
        else:
            # 其他出行链类型的实现...
            trip_1 = self.__genPrivateTrip_CurrentToLeisure(f"trip{daynum}_1", current_taz, current_edge, weekday)
            trip_2 = self.__genPrivateTrip_LeisureToHome(f"trip{daynum}_2", trip_1, weekday)
            ev._add_trip(daynum, trip_1)
            if trip_2.DPTT < 86400:
                ev._add_trip(daynum, trip_2)

    def __genPrivateTrip_CurrentToWork(self, trip_id: str, from_taz: str, from_edge: str, weekday: bool) -> _TripInner:
        """从当前位置到工作地"""
        depart_time_hour = max(0, min(24, self.private_depart_home_weekday.sample()))
        depart_time_min = int(depart_time_hour * 60)

        to_TAZ, to_EDGE, route = self.__getNextTAZandPlace(from_taz, from_edge, "Work")
        return _TripInner(trip_id, depart_time_min * 60, from_taz, from_edge,
                         to_TAZ, to_EDGE, route, "Work")

    def __genPrivateTrip_CurrentToLeisure(self, trip_id: str, from_taz: str, from_edge: str, weekday: bool) -> _TripInner:
        """从当前位置到休闲场所"""
        if weekday:
            depart_time_hour = max(0, min(24, self.private_depart_home_weekday.sample()))
        else:
            if random.random() < 0.5:
                depart_time_hour = max(0, min(24, self.private_depart_home_weekend_am.sample()))
            else:
                depart_time_hour = max(0, min(24, self.private_depart_home_weekend_pm.sample()))

        depart_time_min = int(depart_time_hour * 60)

        leisure_types = ["Relax", "Other"]
        dest_type = random.choice(leisure_types)

        to_TAZ, to_EDGE, route = self.__getNextTAZandPlace(from_taz, from_edge, dest_type)
        return _TripInner(trip_id, depart_time_min * 60, from_taz, from_edge,
                         to_TAZ, to_EDGE, route, dest_type)

    def __genTripsChain_Taxi(self, ev: _EVInner):
        """生成出租车第一天出行链"""
        daynum = 0
        weekday = True

        # 出租车从随机位置开始运营
        current_taz = random.choice(list(self.dic_taztype.values())[0])
        current_edge = random.choice(self.dic_taz[current_taz])

        # 生成一天的多次行程
        trip_count = 0
        current_time = 0

        while current_time < 86400 and trip_count < self.taxi_daily_trips:
            # 采样出发时间
            if trip_count == 0:
                depart_hour = self.__sample_taxi_departure_time()
            else:
                # 后续行程间隔30-90分钟
                interval_minutes = random.uniform(30, 90)
                depart_hour = current_time / 3600 + interval_minutes / 60

            if depart_hour >= 22:  # 超过服务时间
                break

            depart_time = int(depart_hour * 3600)

            # 随机选择目的地类型（出租车服务各种需求）
            dest_types = ["Home", "Work", "Relax", "Other"]
            dest_weights = [0.3, 0.3, 0.2, 0.2]  # 家和工作地需求较高
            dest_type = random.choices(dest_types, weights=dest_weights)[0]

            # 生成行程
            to_TAZ, to_EDGE, route = self.__getNextTAZandPlace(current_taz, current_edge, dest_type)

            trip = _TripInner(
                f"trip{daynum}_{trip_count + 1}",
                depart_time,
                current_taz, current_edge,
                to_TAZ, to_EDGE, route, dest_type
            )

            ev._add_trip(daynum, trip)

            # 更新当前位置和时间
            current_taz = to_TAZ
            current_edge = to_EDGE
            current_time = depart_time + random.uniform(900, 1800)  # 行程时间15-30分钟
            trip_count += 1

    def __genTripsChainA_Taxi(self, ev: _EVInner, daynum: int):
        """生成出租车后续天出行链"""
        weekday = self.__is_weekday(daynum)

        # 从前一天最后位置开始
        last_trip = ev.trips[-1]
        current_taz = last_trip.toTAZ
        current_edge = last_trip.route[-1]

        # 类似第一天的逻辑，但可能调整运营强度
        trip_count = 0
        current_time = 0
        daily_trips = self.taxi_daily_trips if weekday else int(self.taxi_daily_trips * 0.8)

        while current_time < 86400 and trip_count < daily_trips:
            # 采样出发时间
            if trip_count == 0:
                depart_hour = self.__sample_taxi_departure_time()
            else:
                interval_minutes = random.uniform(30, 90)
                depart_hour = current_time / 3600 + interval_minutes / 60

            if depart_hour >= 22:
                break

            depart_time = int(depart_hour * 3600)

            # 随机选择目的地类型
            dest_types = ["Home", "Work", "Relax", "Other"]
            dest_weights = [0.3, 0.3, 0.2, 0.2]
            dest_type = random.choices(dest_types, weights=dest_weights)[0]

            # 生成行程
            to_TAZ, to_EDGE, route = self.__getNextTAZandPlace(current_taz, current_edge, dest_type)

            trip = _TripInner(
                f"trip{daynum}_{trip_count + 1}",
                depart_time,
                current_taz, current_edge,
                to_TAZ, to_EDGE, route, dest_type
            )

            ev._add_trip(daynum, trip)

            # 更新当前位置和时间
            current_taz = to_TAZ
            current_edge = to_EDGE
            current_time = depart_time + random.uniform(900, 1800)
            trip_count += 1





    def genEVs(
        self, N: int, fname: Optional[str] = None, day_count: int = 7, silent: bool = False, **kwargs
    ) -> EVDict:
        """
        Generate EV and trips of N vehicles.
        The generated vehicles are returned as an EVDict instance, and will be saved to the file if fname is provided.
        The vehicles will not be held in the buffer.
            N: Number of vehicles
            fname: Saved file name (if None, not saved)
            day_count: Number of days
            silent: Whether silent mode
            v2g_prop: Proportion of users willing to participate in V2G
            omega: PDFunc | None = None,
            krel: PDFunc | None = None,
            ksc: PDFunc | None = None,
            kfc: PDFunc | None = None,
            kv2g: PDFunc | None = None
        """
        st_time = time.time()
        last_print_time = 0
        saver = _xmlSaver(fname) if fname else None
        ret = EVDict()
        for i in range(0, N):
            ev = self.__genEV("v" + str(i), day_count, **kwargs)
            ret.add(ev.toEV())
            if saver:
                saver.write(ev)
            if not silent and time.time()-last_print_time>1:
                print(f"\r{i+1}/{N}, {(i+1)/N*100:.2f}%", end="")
                last_print_time=time.time()
        if not silent:
            print(f"\r{N}/{N}, 100.00%")
            print(f"完成，用时{round(time.time() - st_time, 1)}秒")
        if saver:
            saver.close()
        return ret

class ManualEVsGenerator:
    """手动向缓冲区添加电动汽车的类"""
    def __init__(self):
        self.__evs:Dict[str, _EVInner] = {}
    
    def addEV(self, vid:str, bcap_kWh:float, range_km:float, efc_rate_kW:float, 
            esc_rate_kW:float, max_V2G_kW:float, soc:float, omega:float, 
            krel:float, ksc:float, kfc:float, kv2g:float, cache_route:bool = False) -> _EVInner:
        """
        Add an EV to the generator's buffer
            vid: Vehicle ID
            vtype: Vehicle type
            soc: State of charge (0.0~1.0)
        """
        if vid in self.__evs:
            raise ValueError(f"Vehicle ID {vid} already exists.")
        ev = _EVInner(vid, VehicleType(id=-1, bcap_kWh=bcap_kWh, range_km=range_km,
            efc_rate_kW=efc_rate_kW, esc_rate_kW=esc_rate_kW, max_V2G_kW=max_V2G_kW), 
            soc, omega=omega, krel=krel, ksc=ksc, kfc=kfc, kv2g=kv2g, cache_route=cache_route)
        self.__evs[vid] = ev
        return ev
    
    def dumpEVs(self, fname: str):
        """
        Dump all EVs in the buffer to a file
            fname: File name
        """
        saver = _xmlSaver(fname)
        for ev in self.__evs.values():
            saver.write(ev)
        saver.close()
    
    def getEVs(self) -> EVDict:
        """
        Get all EVs in the buffer as an EVDict instance
        """
        ret = EVDict()
        for ev in self.__evs.values():
            ret.add(ev.toEV())
        return ret