from itertools import chain
import operator
from collections import defaultdict
from ..traffic import VehStatus
from .base import *
import traci

FILE_EV_BY_TYPE = "ev_by_type"
VEHICLE_TYPES = ["private", "taxi"]
# 专门针对充电负荷图的统计指标
EV_ATTRIB_BY_TYPE = [
    "charge_power_kW",      # 总充电功率 (kW)
    "fast_charge_power_kW", # 快充功率 (kW)
    "slow_charge_power_kW", # 慢充功率 (kW)
    "v2g_power_kW",         # V2G放电功率 (kW，负值表示放电)
    "charging_count",       # 正在充电的车辆数量
    "fast_charging_count",  # 快充车辆数量
    "slow_charging_count",  # 慢充车辆数量
    "avg_charge_power_kW"   # 平均充电功率 (kW)
]

class StaEVByType(StaBase):
    """按车辆类型分类的EV统计类"""

    def __init__(self, path: str, tinst: TrafficInst, plugins: dict[str, PluginBase]):
        super().__init__(
            FILE_EV_BY_TYPE,
            path,
            cross_list(VEHICLE_TYPES, EV_ATTRIB_BY_TYPE),
            tinst,
            plugins
        )

    def GetData(self, inst: TrafficInst, plugins: dict[str, PluginBase]) -> Iterable[Any]:
        """按车辆类型分组统计充电负荷数据（基于充电站负荷分配）"""
        # 初始化各类型车辆的充电数据
        vehicle_data = {
            "private": {
                "total_power": 0.0,
                "fast_power": 0.0,
                "slow_power": 0.0,
                "v2g_power": 0.0,
                "count": 0,
                "fast_count": 0,
                "slow_count": 0
            },
            "taxi": {
                "total_power": 0.0,
                "fast_power": 0.0,
                "slow_power": 0.0,
                "v2g_power": 0.0,
                "count": 0,
                "fast_count": 0,
                "slow_count": 0
            }
        }

        # 1. 处理FCS（快充站）
        for fcs in inst.FCSList:
            station_power_kW = fcs.Pc_kW  # 直接使用kW单位的功率
            if station_power_kW > 0:  # 有充电负荷
                vehicles_in_station = list(fcs.vehicles())
                if vehicles_in_station:
                    # 统计各类型车辆数量
                    private_count = 0
                    taxi_count = 0

                    for vid in vehicles_in_station:
                        if vid in inst.vehicles:
                            veh = inst.vehicles[vid]
                            if veh.is_private_car:
                                private_count += 1
                            elif veh.is_taxi:
                                taxi_count += 1

                    total_count = private_count + taxi_count

                    if total_count > 0:
                        # 按比例分配充电功率
                        private_power = station_power_kW * private_count / total_count
                        taxi_power = station_power_kW * taxi_count / total_count

                        vehicle_data["private"]["total_power"] += private_power
                        vehicle_data["private"]["fast_power"] += private_power
                        vehicle_data["private"]["count"] += private_count
                        vehicle_data["private"]["fast_count"] += private_count

                        vehicle_data["taxi"]["total_power"] += taxi_power
                        vehicle_data["taxi"]["fast_power"] += taxi_power
                        vehicle_data["taxi"]["count"] += taxi_count
                        vehicle_data["taxi"]["fast_count"] += taxi_count

        # 2. 处理SCS（慢充站）
        for scs in inst.SCSList:
            station_power_kW = scs.Pc_kW  # 直接使用kW单位的功率
            if station_power_kW > 0:  # 有充电负荷
                vehicles_in_station = list(scs.vehicles())
                if vehicles_in_station:
                    # 统计各类型车辆数量
                    private_count = 0
                    taxi_count = 0

                    for vid in vehicles_in_station:
                        if vid in inst.vehicles:
                            veh = inst.vehicles[vid]
                            if veh.is_private_car:
                                private_count += 1
                            elif veh.is_taxi:
                                taxi_count += 1

                    total_count = private_count + taxi_count

                    if total_count > 0:
                        # 按比例分配充电功率
                        private_power = station_power_kW * private_count / total_count
                        taxi_power = station_power_kW * taxi_count / total_count

                        vehicle_data["private"]["total_power"] += private_power
                        vehicle_data["private"]["slow_power"] += private_power
                        vehicle_data["private"]["count"] += private_count
                        vehicle_data["private"]["slow_count"] += private_count

                        vehicle_data["taxi"]["total_power"] += taxi_power
                        vehicle_data["taxi"]["slow_power"] += taxi_power
                        vehicle_data["taxi"]["count"] += taxi_count
                        vehicle_data["taxi"]["slow_count"] += taxi_count

        # 3. 组装返回数据 - 按照cross_list的顺序（先按属性分组）
        result_data = []

        # cross_list的顺序是：先按属性分组，每个属性包含所有车辆类型
        for attrib in EV_ATTRIB_BY_TYPE:
            for vtype in VEHICLE_TYPES:
                data = vehicle_data[vtype]

                if attrib == "charge_power_kW":
                    result_data.append(data["total_power"])
                elif attrib == "fast_charge_power_kW":
                    result_data.append(data["fast_power"])
                elif attrib == "slow_charge_power_kW":
                    result_data.append(data["slow_power"])
                elif attrib == "v2g_power_kW":
                    result_data.append(data["v2g_power"])
                elif attrib == "charging_count":
                    result_data.append(data["count"])
                elif attrib == "fast_charging_count":
                    result_data.append(data["fast_count"])
                elif attrib == "slow_charging_count":
                    result_data.append(data["slow_count"])
                elif attrib == "avg_charge_power_kW":
                    # 计算平均充电功率
                    avg_power = data["total_power"] / data["count"] if data["count"] > 0 else 0.0
                    result_data.append(avg_power)

        return result_data
