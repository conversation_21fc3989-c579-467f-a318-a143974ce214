from dataclasses import dataclass
import gzip, random
from typing import Any, List, Optional, Sequence, Union
from feasytools.pdf import *

from ..traffic import EV,Trip

@dataclass
class VehicleType:
    """车辆类型数据类"""
    id: int              # 车辆类型ID
    bcap_kWh: float      # 电池容量(kWh)
    range_km: float      # 续航里程(km)
    efc_rate_kW: float   # 快充功率(kW)
    esc_rate_kW: float   # 慢充功率(kW)
    max_V2G_kW: float    # 最大V2G功率(kW)
    category: str = "general"      # 车辆类别：private/taxi

    @property
    def is_private_car(self) -> bool:
        """判断是否为私家车"""
        return self.category == "private" or 10 <= self.id <= 12

    @property
    def is_taxi(self) -> bool:
        """判断是否为出租车"""
        return self.category == "taxi" or 20 <= self.id <= 21


    
def random_diff(seq:Sequence[Any], exclude:Any):
    """
    从序列中随机选择一个不等于exclude的元素
    """
    ret = exclude
    if len(seq) == 1 and seq[0] == exclude:
        raise RuntimeError("错误: 随机选择无法排除所有选项")
    while ret == exclude:
        ret = random.choice(seq)
    return ret

class _TripInner:
    """内部行程类"""
    def __init__(self, trip_id:str, depart_time:Union[str,int], from_TAZ:str, from_EDGE:str,
            to_TAZ:str, to_EDGE:str, route:List[str], next_type_place:str, fixed_route:Optional[bool]=None):
        '''
        初始化行程对象
            trip_id: 唯一行程ID
            depart_time: 出发时间（自午夜起的秒数）
                可以是字符串或整数。如果是字符串，应该可以转换为整数。
            from_TAZ: 起点TAZ ID
            from_EDGE: 起点边ID
            to_TAZ: 终点TAZ ID
            to_EDGE: 终点边ID
            route: 表示路线的边ID列表，应至少有2个元素
            next_type_place: 行程结束地点的类型
            fixed_route: 路线是否固定
        '''
        self.id = trip_id
        self.DPTT = int(depart_time) # departure time in seconds since midnight
        self.frE = from_EDGE
        self.frTAZ = from_TAZ
        self.toE = to_EDGE
        self.toTAZ = to_TAZ
        assert isinstance(route, list) and len(route) >= 2, "Route should be a list with at least 2 elements"
        self.route = route
        self.NTP = next_type_place
        self.fixed_route = fixed_route
    
    def toXML(self, daynum:int) -> str:
        return (f'\n<trip id="{self.id}" depart="{self.DPTT + 86400 * daynum}" ' + 
            f'fromTaz="{self.frTAZ}" toTaz="{self.toTAZ}" route_edges="{" ".join(self.route)}" ' + 
            f'fixed_route="{self.fixed_route}" />')
    
    def toTrip(self, daynum:int) -> Trip:
        return Trip(self.id, self.DPTT + 86400 * daynum, self.frTAZ, self.toTAZ, self.route)

PDFuncLike = Union[None, float, PDFunc]
def _impl_PDFuncLike(x:PDFuncLike, default:PDFunc) -> float:
    if x is None:
        return default.sample()
    elif isinstance(x, float):
        return x
    elif isinstance(x, PDFunc):
        return x.sample()
    raise TypeError("x must be None, float or PDFunc")
    
class _EVInner:
    """
    用于生成行程的电动汽车类
    """

    def __init__(self, veh_id: str, vT:VehicleType, soc:float, v2g_prop:float = 1.0+1e-4,
        omega:PDFuncLike = None, krel:PDFuncLike = None,
        ksc:PDFuncLike = None, kfc:PDFuncLike = None,
        kv2g:PDFuncLike = None, cache_route:bool = False,
    ):
        '''
        初始化电动汽车对象
            veh_id: 车辆ID
            vT: 车辆类型
            soc: 电池荷电状态
            v2g_prop: 车辆参与V2G的概率。值>=1.0表示总是参与V2G
            omega: omega的概率分布函数。None表示5到10之间的随机均匀分布
                omega表示用户对充电成本的敏感性。omega越大表示越不敏感
            krel: krel的概率分布函数。None表示1到1.2之间的随机均匀分布
                krel表示用户对距离的估计。krel越大表示用户低估距离
            ksc: ksc的概率分布函数。None表示0.4到0.6之间的随机均匀分布
                ksc表示慢充的SOC阈值
            kfc: kfc的概率分布函数。None表示0.2到0.25之间的随机均匀分布
                kfc表示中途快充的SOC阈值
            kv2g: kv2g的概率分布函数。None表示0.65到0.75之间的随机均匀分布
                kv2g表示可用于V2G的电池SOC阈值
            cache_route: 是否记住路线以供进一步使用
        '''
        self.vehicle_id = veh_id
        self.vType = vT  # 保存车辆类型信息
        self.bcap = vT.bcap_kWh
        self.soc = soc
        self.consump_Whpm = vT.bcap_kWh / vT.range_km  # kWh/km = Wh/m
        self.efc_rate_kW = vT.efc_rate_kW
        self.esc_rate_kW = vT.esc_rate_kW
        self.max_v2g_rate_kW = vT.max_V2G_kW
        self.omega = _impl_PDFuncLike(omega, PDUniform(5.0, 10.0))
        self.krel = _impl_PDFuncLike(krel, PDUniform(1.0, 1.2))
        self.ksc = _impl_PDFuncLike(ksc, PDUniform(0.4, 0.6))
        self.kfc = _impl_PDFuncLike(kfc, PDUniform(0.2, 0.25))
        self.cache_route = cache_route
        if v2g_prop >= 1.0 or random.random() < v2g_prop:
            self.kv2g = _impl_PDFuncLike(kv2g, PDUniform(0.65, 0.75))
        else:
            self.kv2g = 1 + 1e-4
        self.trips:List[_TripInner] = []
        self.daynum:List[int] = []
    
    def _add_trip(self, daynum: int, trip_dict: _TripInner):
        self.daynum.append(daynum)
        self.trips.append(trip_dict)
    
    def addTrip(self, trip_id:str, depart_time:int, from_TAZ:str, from_edge:str, to_TAZ:str, to_edge:str,
            route:List[str], fixed_route:Optional[bool] = None, daynum:int = -1):
        '''
        为电动汽车添加行程
            trip_id: 唯一行程ID
            depart_time: 当天午夜起的出发时间（秒）
                当小于86400且`daynum`>=0时，被视为`daynum`指定日期的时间
                否则，被视为仿真开始后的确切时间
            from_TAZ: 起点TAZ ID
            from_EDGE: 起点边ID
            to_TAZ: 终点TAZ ID
            to_EDGE: 终点边ID
            route: 表示路线的边ID列表，应至少有2个元素
                当路线只有2个元素时，被视为只有起点和终点边
            fixed_route: 路线是否固定
                True表示路线固定
                False表示路线不固定，每次使用行程时必须重新计算
                None表示未指定，路线是否固定将由仿真决定
            daynum: 天数，从0开始。-1表示行程不在特定日期，`depart_time`是仿真开始后的确切时间
        异常:
            AssertionError: 如果指定了`daynum`且`depart_time`不小于86400
        '''
        if daynum < 0:
            daynum = depart_time // 86400
            depart_time = depart_time % 86400
        else:
            assert 0 <= depart_time and depart_time < 86400, "When daynum is specified, depart_time must be less than 86400."
        self.daynum.append(daynum)
        depart_time = int(depart_time) if isinstance(depart_time, str) else depart_time
        self.trips.append(_TripInner(trip_id, depart_time, from_TAZ, from_edge, 
            to_TAZ, to_edge, route, "", fixed_route))

    def toXML(self) -> str:
        ret = (
            f'<vehicle id="{self.vehicle_id}" soc="{self.soc:.4f}" bcap="{self.bcap:.4f}" c="{self.consump_Whpm:.8f}"'
            + f' rf="{self.efc_rate_kW:.4f}" rs="{self.esc_rate_kW:.4f}" rv="{self.max_v2g_rate_kW:.4f}" omega="{self.omega:.6f}"'
            + f'\n  kf="{self.kfc:.4f}" ks="{self.ksc:.4f}" kv="{self.kv2g:.4f}" kr="{self.krel:.4f}"'
            + f' eta_c="0.9" eta_d="0.9" rmod="Linear" cache_route="{self.cache_route}"'
            + f'\n  vtype_id="{self.vType.id}" vtype_category="{self.vType.category}">'
        )
        for d, tr in zip(self.daynum, self.trips):
            ret += tr.toXML(d)
        ret += "\n</vehicle>\n"
        return ret

    def toEV(self) -> EV:
        trips = [m.toTrip(daynum) for m, daynum in zip(self.trips, self.daynum)]
        return EV(
            self.vehicle_id,
            trips,
            0.9,
            0.9,
            self.bcap,
            self.soc,
            self.consump_Whpm,
            self.efc_rate_kW,
            self.esc_rate_kW,
            self.max_v2g_rate_kW,
            self.omega,
            self.krel,
            self.kfc,
            self.ksc,
            self.kv2g,
            "Linear",
            cache_route=self.cache_route,
            vtype_id=self.vType.id,
            vtype_category=self.vType.category,
        )


class _xmlSaver:
    """用于保存XML文件的类"""

    def __init__(self, path: str):
        if path.endswith(".gz"):
            self.a = gzip.open(path, "wt")
        else:
            self.a = open(path, "w")
        self.a.write("<root>\n")

    def write(self, e: _EVInner):
        self.a.write(e.toXML())

    def close(self):
        self.a.write("</root>")
        self.a.close()