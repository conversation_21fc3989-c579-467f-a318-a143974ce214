"""
交通系统实例模块
管理整个交通仿真系统的核心类
"""
from itertools import chain
from pathlib import Path
import platform, random
import numpy as np
from sumolib.net import readNet, Net
from sumolib.net.edge import Edge
from feasytools import PQueue, Point

from .trip import TripsLogger
from .cslist import *
from .ev import *
from .charging_decision import ChargingDecisionManager

from .utils import random_string, TWeights
from .params import ROAD_DISTANCE_SCALE_FACTOR

import traci

from traci._simulation import Stage

TC = traci.constants

class TrafficInst:
    """交通系统实例，协调车辆、充电站和路径规划"""
    def __find_route(self, e1: str, e2: str) -> Stage:
        ret:Stage = traci.simulation.findRoute(
            e1, e2, routingMode=TC.ROUTING_MODE_AGGREGATED
        )
        if len(ret.edges) == 0:
            if SUPPRESS_ROUTE_NOT_FOUND:
                ret = Stage(edges=[e1,e2], length=1e9, travelTime=1e9)
                print(f"错误: 无法找到从{e1}到{e2}的路径")
            else:
                raise RuntimeError(f"错误: 无法找到从{e1}到{e2}的路径")
        else:
            # 对动态路径应用距离缩放因子
            ret.length *= ROAD_DISTANCE_SCALE_FACTOR
        return ret

    def __find_route_trip(self, t: Trip, cache_route:bool = False) -> Stage:
        if t.fixed_route:
            einst:list[Edge] = [self.__rnet.getEdge(e) for e in t.route]
            # 应用道路距离缩放因子
            original_length = sum(e.getLength() for e in einst)
            scaled_length = original_length * ROAD_DISTANCE_SCALE_FACTOR
            st = Stage(
                edges = t.route,
                length = scaled_length,
                travelTime = sum(e.getLength() / e.getSpeed() for e in einst),
            )
            return st
        else:
            st = self.__find_route(t.route[0], t.route[-1])
            # 对动态路径也应用缩放因子
            st.length *= ROAD_DISTANCE_SCALE_FACTOR
            if cache_route:
                t.route = st.edges
            return st
    


    def __init__(
        self,
        road_net_file: str,
        start_time: int,
        step_len: int,
        end_time: int,
        clogfile: str,
        seed: int = 0, *,
        vehfile: str, veh_obj:Optional[EVDict] = None,
        fcsfile: str, fcs_obj:Optional[CSList] = None,
        scsfile: str, scs_obj:Optional[CSList] = None,

        routing_algo:str = "dijkstra",
    ):
        """
        TrafficInst initialization
            road_net_file: SUMO road network configuration file
            start_time: Simulation start time
            end_time: Simulation end time
            clogfile: Log file path
            seed: Randomization seed
            vehfile: Vehicle information and itinerary file
            fcsfile: Fast charging station list file
            scsfile: Slow charging station list file

            routing_algo: Routing algorithm
        """
        random.seed(seed)
        self.__gui = None
        self.__logger = TripsLogger(clogfile)
        self.__ralgo = "dijkstra"  # 固定使用dijkstra算法
        

        self.__vehfile = vehfile
        self.__fcsfile = fcsfile
        self.__scsfile = scsfile
        self.__ctime: int = start_time
        self.__stime: int = start_time
        self.__step_len: int = step_len
        self.__etime: int = end_time
        
        # 读取道路网络
        self.__rnet: Net = readNet(road_net_file)
        self.__edges: list[Edge] = self.__rnet.getEdges()
        # 获取所有道路名称
        self.__names: list[str] = [e.getID() for e in self.__edges]




        
        # 加载车辆
        self._fQ = PQueue()  # Fault queue
        self._que = PQueue()  # Departure queue
        self._VEHs = veh_obj if veh_obj else EVDict(vehfile)

        # 加载充电站
        self._fcs:CSList[FCS] = fcs_obj if fcs_obj else CSList(self._VEHs, filePath=fcsfile, csType=FCS)
        #if len(self._fcs) == 0:
        #    raise RuntimeError("No fast charging station found")
        self._scs:CSList[SCS] = scs_obj if scs_obj else CSList(self._VEHs, filePath=scsfile, csType=SCS)
        #if len(self._scs) == 0:
        #    raise RuntimeError("No slow charging station found")
        self.__names_fcs: list[str] = [cs.name for cs in self._fcs]
        self.__names_scs: list[str] = [cs.name for cs in self._scs]

        # 初始化充电决策管理器
        self._charging_decision_manager = ChargingDecisionManager(self._fcs, self.__find_route)

        # 将车辆加载到充电站并准备出发
        for veh in self._VEHs.values():
            self._que.push(veh.trip.depart_time, veh.ID)
            # 有20%的概率添加到可充电停车点
            if veh.SOC < veh.ksc or random.random() <= 0:
                self.__start_charging_SCS(veh)

    @property
    def veh_file(self):
        """车辆信息和行程文件"""
        return self.__vehfile

    @property
    def fcs_file(self):
        """快充站列表文件"""
        return self.__fcsfile
    
    @property
    def scs_file(self):
        """慢充站列表文件"""
        return self.__scsfile
    
    @property
    def start_time(self):
        """仿真开始时间"""
        return self.__stime

    @property
    def end_time(self):
        """仿真结束时间"""
        return self.__etime

    @property
    def step_len(self):
        """仿真步长"""
        return self.__step_len
    
    @property
    def current_time(self):
        """当前时间"""
        return self.__ctime

    @property
    def FCSList(self)->CSList[FCS]:
        """快充站列表"""
        return self._fcs

    @property
    def SCSList(self)->CSList[SCS]:
        """慢充站列表"""
        return self._scs

    @property
    def vehicles(self) -> EVDict:
        """车辆字典，键为车辆ID，值为EV实例"""
        return self._VEHs

    def __add_veh(self, veh_id: str, route: list[str]):
        self._VEHs[veh_id].clear_odometer()
        rou_id = random_string(16)
        traci.route.add(rou_id, route)
        traci.vehicle.add(veh_id, rou_id)
        traci.vehicle.subscribe(veh_id, (TC.VAR_DISTANCE,))
    
    def __add_veh2(self, veh_id:str, st_edge:str, ed_edge:str, agg_routing:bool = False):
        self._VEHs[veh_id].clear_odometer()
        traci.vehicle.add(veh_id, "")
        traci.vehicle.setRoute(veh_id, [st_edge])
        if agg_routing:
            traci.vehicle.setRoutingMode(veh_id, TC.ROUTING_MODE_AGGREGATED)
        traci.vehicle.changeTarget(veh_id, ed_edge)
        traci.vehicle.subscribe(veh_id, (TC.VAR_DISTANCE,))

    @property
    def edges(self) -> list[Edge]:
        """获取所有道路"""
        return self.__edges

    @property
    def trips_iterator(self):
        """获取所有行程的迭代器"""
        return chain(*(x.trips for x in self._VEHs.values()))

    def get_edge_names(self) -> list[str]:
        """获取所有道路的名称"""
        return self.__names
    
    def __sel_best_CS(
        self, veh: EV, omega: float, current_edge: Optional[str] = None
    ) -> tuple[list[str], TWeights]:
        """
        Select the nearest available charging station based on the edge where the car is currently located, and return the path and average weight
            veh: Vehicle instance
            omega: Weight
            current_edge: Current road, if None, it will be automatically obtained
        Return:
            Path(list[str]), Weight(tuple[float,float,float])
            If no charging station is found, return [],(-1,-1,-1)
        """
        # 使用新的充电决策模块进行充电站选择
        c_edge = (
            traci.vehicle.getRoadID(veh.ID) if current_edge is None else current_edge
        )

        # 判断当前是否为工作日（简单实现：周一到周五为工作日）
        is_weekday = (self.__ctime // 86400) % 7 < 5  # 假设仿真从周一开始
        return self._charging_decision_manager.select_charging_station(
            veh, omega, c_edge, self.__ctime, is_weekday
        )

    def __start_trip(self, veh_id: str) -> tuple[bool, Optional[TWeights]]:
        """
        开始车辆的当前行程
            veh_id: 车辆ID
        返回:
            出发成功: True, 快充站选择权重（如果需要快充）
            出发失败: False, None
        """
        weights = None
        veh = self._VEHs[veh_id]
        trip = veh.trip
        direct_depart = True

        if ENABLE_DIST_BASED_CHARGING_DECISION:
            stage = self.__find_route_trip(trip, veh._cache_route)
            # 判断电池是否充足
            direct_depart = veh.is_batt_enough(stage.length)
        else:
            # 判断电动汽车是否需要快充
            stage = None
            direct_depart = veh.SOC >= veh.kfc
        if direct_depart:  # 直接出发
            veh.target_CS = None
            veh.charge_target = veh.full_battery
            if stage:
                self.__add_veh(veh_id, stage.edges)
            else:
                self.__add_veh2(veh_id, trip.depart_edge, trip.arrive_edge)
        else:  # 途中充电一次
            e:Edge = self.__rnet.getEdge(trip.depart_edge)
            sp = e.getShape()
            assert isinstance(sp, list) and len(sp) > 0
            route, weights = self.__sel_best_CS(veh, veh.omega, trip.depart_edge, Point(*sp[0]))
            if len(route) == 0:
                # 电量不足以驾驶到任何充电站，需要充电一段时间
                veh.target_CS = None
                return False, None
            else: # 找到充电站
                veh.target_CS = route[-1]
                self.__add_veh(veh_id, route)
        # 停止车辆慢充并将其添加到等待出发集合
        self._scs.pop_veh(veh_id)
        veh.stop_charging()
        veh.status = VehStatus.Pending
        return True, weights

    def __end_trip(self, veh_id: str):
        """
        End the current trip of a vehicle and add its next trip to the departure queue.
        If the destination of the trip meets the charging conditions, try to charge.
            veh_id: Vehicle ID
        """
        veh = self._VEHs[veh_id]
        veh.status = VehStatus.Parking
        arr_sta = TripsLogger.ARRIVAL_NO_CHARGE
        if veh.SOC < veh.ksc:
            # 添加到慢充站
            if self.__start_charging_SCS(veh):
                arr_sta = TripsLogger.ARRIVAL_CHARGE_SUCCESSFULLY
            else:
                arr_sta = TripsLogger.ARRIVAL_CHARGE_FAILED
        else:
            arr_sta = TripsLogger.ARRIVAL_NO_CHARGE
        self.__logger.arrive(self.__ctime, veh, arr_sta)
        tid = veh.next_trip()
        if tid != -1:
            ntrip = veh.trip
            self._que.push(ntrip.depart_time, veh_id)

    def __start_charging_SCS(self, veh: EV) -> bool:
        """
        Make a vehicle enter the charging state (slow charging station)
            veh: Vehicle instance
        """
        try:
            self._scs.add_veh(veh.ID, veh.trip.arrive_edge)
            return True
        except:
            return False

    def __start_charging_FCS(self, veh: EV):
        """
        Make a vehicle enter the charging state (fast charging station)
            veh: Vehicle instance
        """
        veh.status = VehStatus.Charging
        assert isinstance(veh.target_CS, str)
        if ENABLE_DIST_BASED_CHARGING_QUANTITY:
            ch_tar = (
                veh.consumption
                * self.__find_route_static(veh.target_CS, veh.trip.arrive_edge).length
            )
            if ch_tar > veh.full_battery:
                # 即使中途电池充满，车辆仍无法到达目的地
                self.__logger.warn_smallcap(self.__ctime, veh, ch_tar)
            veh.charge_target = min(
                veh.full_battery, max(veh.full_battery * 0.8, veh.krel * ch_tar)
            )
        else:
            veh.charge_target = veh.full_battery
        self._fcs.add_veh(veh.ID, veh.target_CS)
        self.__logger.arrive_CS(self.__ctime, veh, veh.target_CS)

    def __end_charging_FCS(self, veh: EV):
        """
        Make a vehicle end charging and depart (fast charging station)
            veh: Vehicle instance
        """
        if veh.target_CS is None:
            raise RuntimeError(
                f"Runtime error: {self.__ctime}, {veh.brief()}, {veh.status}"
            )
        trip = veh.trip
        self.__logger.depart_CS(self.__ctime, veh, veh.target_CS)
        
        self.__add_veh2(veh.ID, veh.target_CS, trip.arrive_edge)
        veh.target_CS = None
        veh.charge_target = veh.full_battery
        veh.status = VehStatus.Pending
        veh.stop_charging()

    def __get_nearest_CS(
        self, cur_edge: str
    ) -> tuple[Optional[str], float, Optional[Stage]]:
        """
        Find the nearest charging station
            cur_edge: Current road
        """
        # 使用新的充电决策模块获取最近充电站
        return self._charging_decision_manager.get_nearest_charging_station(cur_edge, self.__ctime)

    #@FEasyTimer
    def __batch_depart(self) -> dict[str, Optional[TWeights]]:
        """
        将到达出发队列的所有车辆发出
            self.__ctime: 当前时间，以秒为单位
        返回:
            出发字典，键为车辆ID，值为快充站选择参数（如果不需要去快充站则为None）
        """
        ret = {}
        while not self._que.empty() and self._que.top[0] <= self.__ctime:
            depart_time, veh_id = self._que.pop()
            veh = self._VEHs[veh_id]
            trip = veh.trip
            success, weights = self.__start_trip(veh_id)
            if success:
                depart_delay = max(0, self.__ctime - depart_time)
                self.__logger.depart(self.__ctime, veh, depart_delay, veh.target_CS, weights)
                ret[veh_id] = weights
            else:
                cs_name, cs_dist, cs_stage = self.__get_nearest_CS(trip.depart_edge)
                batt_req = cs_dist * veh.consumption * veh.krel
                if self._scs.has_veh(veh.ID):
                    # 插入充电桩，可以等待
                    delay = int(1 + (batt_req - veh.battery) / veh.rate)
                    self.__logger.depart_delay(self.__ctime, veh, batt_req, delay)
                    self._que.push(depart_time + delay, veh_id)
                else:
                    # 未插入充电桩，送至最近的快充站（消耗2倍行驶时间）
                    veh.status = VehStatus.Depleted
                    assert cs_name is not None and cs_stage is not None, "No FCS found, please check the configuration"
                    veh.target_CS = cs_name
                    trT = int(self.__ctime + 2 * cs_stage.travelTime)
                    self._fQ.push(trT, veh.ID)
                    self.__logger.depart_failed(self.__ctime, veh, batt_req, cs_name, trT)
        return ret

    def __FCS_update(self, sec: int):
        """
        充电站更新：为充电站内所有车辆充电，并发出充电完成的车辆
            sec: 仿真秒数
        """
        veh_ids = self._fcs.update(sec, self.__ctime)
        #veh_ids.sort()
        for i in veh_ids:
            self.__end_charging_FCS(self._VEHs[i])

    #@FEasyTimer
    def __SCS_update(self, sec: int):
        """
        停车车辆更新：为充电站内所有停车车辆充电和V2G
            sec: 仿真秒数
        """
        self._scs.update(sec, self.__ctime)

    def get_sta_head(self) -> list[str]:
        """
        Get the edge name corresponding to the return value of get_veh_count and CS_PK_update
        """
        return self.__names_fcs + self.__names_scs

    def get_veh_count(self) -> list[int]:
        """
        Get the number of parked vehicles in all charging station and non-charging station edges
        """
        return self._fcs.get_veh_count() + self._scs.get_veh_count()

    def simulation_start(
        self,
        sumocfg_file: str,
        net_file: str,
        start_time: Optional[int] = None,
        gui: bool = True,
    ):
        """
        Start simulation
            sumocfg_file: SUMO configuration file path
            start_time: Start time (seconds), if not specified, provided by the SUMO configuration file
            gui: Whether to display the graphical interface
        """
        self.__gui = gui
        sumoCmd = [
            "sumo-gui" if self.__gui else "sumo",
            "-c", sumocfg_file,
            "-n", net_file,
            "--no-warnings",
            "--routing-algorithm", self.__ralgo,
        ]
        if start_time is not None:
            sumoCmd.extend(["-b", str(start_time)])
        traci.start(sumoCmd)

        self.__ctime = int(traci.simulation.getTime())
        self.__batch_depart()

    def simulation_step(self, step_len: int):
        """
        仿真步进
            step_len: 步长（秒）
            v2g_demand: V2G需求列表（kWh/s）
        """
        traci.simulationStep(float(self.__ctime + step_len))
        #self.__sumo_step(self.__ctime + step_len)
        new_time = int(traci.simulation.getTime())
        deltaT = new_time - self.__ctime
        self.__ctime = new_time
        
        # 处理到达的车辆
        arr_vehs: List[str] = traci.simulation.getArrivedIDList()

        for v in arr_vehs:
            veh = self._VEHs[v]
            if veh.target_CS is None:
                self.__end_trip(v)
            else:
                self.__start_charging_FCS(self._VEHs[v])

        # 处理行驶中的车辆
        cur_vehs: List[str] = traci.vehicle.getIDList()

        for veh_id in cur_vehs:
            veh = self._VEHs[veh_id]
            veh.drive(traci.vehicle.getDistance(veh_id))
            if veh._elec <= 0:
                # 电池耗尽的车辆将被送至最近的快充站（时间×2）
                veh._sta = VehStatus.Depleted
                cur_edge = traci.vehicle.getRoadID(veh_id)
                veh._cs, _, cs_stage = self.__get_nearest_CS(cur_edge)
                assert cs_stage is not None and veh.target_CS is not None
                trT = int(self.__ctime + 2 * cs_stage.travelTime)
                self._fQ.push(trT, veh_id)
                traci.vehicle.remove(veh_id)
                self.__logger.fault_deplete(self.__ctime, veh, veh.target_CS, trT)
                continue
            if veh._sta == VehStatus.Pending:
                veh._sta = VehStatus.Driving
            if veh._sta == VehStatus.Driving:
                if veh.target_CS is not None and not self._fcs[veh.target_CS].is_online(self.__ctime):
                    # 目标快充站离线，重定向到最近的快充站
                    route, weights = self.__sel_best_CS(veh, veh.omega)
                    if len(route) == 0:  
                        # 电量不足以驾驶到任何充电站，从网络中移除
                        veh._sta = VehStatus.Depleted
                        traci.vehicle.remove(veh_id)
                        self._fQ.push(self.__ctime, veh_id)
                        self.__logger.fault_nocharge(self.__ctime, veh, veh.target_CS)
                        veh.target_CS = None
                    else:  # 找到充电站
                        new_cs = route[-1]
                        traci.vehicle.setRoute(veh_id, route)
                        self.__logger.fault_redirect(self.__ctime, veh, veh.target_CS, new_cs)
                        veh.target_CS = new_cs
            else:
                print(f"Error: {veh.brief()}, {veh._sta}")

        # 处理充电站内车辆和停车车辆
        self.__FCS_update(deltaT)
        self.__SCS_update(deltaT)
        self.__batch_depart()

        # Process faulty vehicles
        while not self._fQ.empty() and self._fQ.top[0] <= self.__ctime:
            _, v = self._fQ.pop()
            self.__start_charging_FCS(self._VEHs[v])

    def simulation_stop(self):
        if self.__gui is None:
            raise RuntimeError("Simulation has not started. Call 'simulation_start' first.")
        traci.close()
        self.__logger.close()
    

        