"""
电动汽车字典模块
管理电动汽车实例的字典容器
"""
from feasytools import RangeList
from .utils import ReadXML
from .params import *
from .ev import EV, Trip

class FloatDictWrapper:
    """浮点数字典包装器，自动转换字符串为浮点数"""
    def __init__(self, d: dict[str, str]):
        self.__d = d

    def get(self, key: str, default: float) -> float:
        return float(self.__d.get(key, default))

class EVDict(dict[str, EV]):
    """电动汽车字典，从XML文件加载车辆数据"""
    def __init__(self, file_path=None):
        super().__init__()
        if file_path is None:
            return
        rt = ReadXML(file_path).getroot()
        if rt is None:
            raise RuntimeError(f"Failed to load EV file {file_path}")

        # 解析每辆车的信息
        for veh in rt:
            trips: list[Trip] = []
            for trip in veh:
                route = trip.attrib["route_edges"].split(" ")
                fixed_route = trip.attrib.get("fixed_route", "none")
                if fixed_route.lower() == "true":
                    fixed_route = True
                elif fixed_route.lower() == "false":
                    fixed_route = False
                else:
                    fixed_route = None
                trips.append(
                    Trip(
                        trip.attrib["id"],
                        int(float(trip.attrib["depart"])),
                        trip.attrib["fromTaz"],
                        trip.attrib["toTaz"],
                        route,
                        fixed_route
                    )
                )
            attr = FloatDictWrapper(veh.attrib)
            elem_sctime = veh.find("sctime")
            elem_v2gtime = veh.find("v2gtime")
            # 注意：以下参数在车辆生成时都会被设定，这里的值仅用于兼容性
            self.add(EV(
                veh.attrib["id"],
                trips,
                attr.get("eta_c", DEFAULT_ETA_CHARGE),
                attr.get("eta_d", DEFAULT_ETA_DISCHARGE),
                attr.get("bcap", 50.0),    # 电池容量：实际由车型定义文件设定
                attr.get("soc", 0.9),      # 初始SOC：实际由车辆生成时随机设定
                attr.get("c", 6.4),        # 耗电速率：实际由车型计算得出
                attr.get("rf", 100.0),     # 快充功率：实际由车型定义文件设定
                attr.get("rs", 7.0),       # 慢充功率：实际由车型定义文件设定
                attr.get("rv", 20.0),      # V2G功率：实际由车型定义文件设定
                attr.get("omega", 75.0),   # omega参数：实际由车辆生成时随机分配
                attr.get("kr", 1.25),      # krel参数：实际由车辆生成时随机分配
                attr.get("kf", 0.2),       # 快充SOC阈值：实际由车辆生成时随机分配(20%-25%)
                attr.get("ks", 0.5),       # 慢充SOC阈值：实际由车辆生成时随机分配(40%-60%)
                attr.get("kv", 0.8),       # V2G SOC阈值：实际由车辆生成时随机分配(65%-75%)
                veh.attrib.get("rmod", DEFAULT_RMOD),
                RangeList(elem_sctime),
                attr.get("max_sc_cost", DEFAULT_MAX_SC_COST),
                RangeList(elem_v2gtime),
                attr.get("min_v2g_earn", DEFAULT_MIN_V2G_EARN),
                veh.attrib.get("cache_route", "false").lower() == "true",
                vtype_id=int(veh.attrib.get("vtype_id", -1)),
                vtype_category=veh.attrib.get("vtype_category", "general"),
            ))

    def add(self, ev: EV):
        """Add a vehicle"""
        super().__setitem__(ev.ID, ev)

    def pop(self, veh_id: str) -> EV:
        """
        Remove a vehicle by ID, return the removed value
        """
        return super().pop(veh_id)
