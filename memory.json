{"type":"entity","name":"电网系统","entityType":"GridSystem","observations":["基准功率单位：MVA (兆伏安)","基准电压单位：kV (千伏)","标准拓扑：IEEE 33节点配电网","节点数量：固定33个母线节点","线路数量：32条输电线路","发电机数量：5台分布式发电机","电压等级：10kV配电网系统","项目数据源：37nodes.grid.xml文件定义完整的IEEE 33节点配电网","负荷特性：每个母线都有详细的24小时负荷曲线数据"]}
{"type":"entity","name":"母线节点","entityType":"ElectricBus","observations":["ID格式：b1到b33的命名规范","坐标系统：WGS84地理坐标系统","电压约束：0.9到1.1标幺值运行范围","负荷数据：24小时有功功率Pd和无功功率Qd时序数据","功率单位：MW (兆瓦) 和 Mvar (兆乏)","电压基准：10kV配电网电压等级"]}
{"type":"entity","name":"输电线路","entityType":"TransmissionLine","observations":["ID格式：l1到l32的命名规范","电气参数：电阻R (ohm) 和电抗X (ohm)","容量约束：最大电流MaxIkA (千安)","连接关系：From和To字段定义起终点母线","长度信息：Length_km字段 (部分为-1.0表示未定义)","阻抗单位：欧姆 (ohm)","拓扑结构：形成IEEE 33节点标准配电网拓扑"]}
{"type":"entity","name":"发电机","entityType":"Generator","observations":["ID格式：g1, g2, g3, g6, g8的命名规范","连接位置：分别连接到b1, b2, b3, b6, b8母线","功率范围：Pmin到Pmax定义有功功率运行范围","无功范围：Qmin到Qmax定义无功功率运行范围","成本函数：二次型成本函数 Cost = CostA*P² + CostB*P + CostC","成本单位：CostA ($/MWh²), CostB ($/MWh), CostC ($)"]}
{"type":"entity","name":"交通网络","entityType":"TrafficNetwork","observations":["仿真平台：SUMO (Simulation of Urban Mobility)","网络文件：37nodes.net.xml格式","网络元素：包含道路、交叉口、信号灯等交通基础设施","配置格式：XML格式的SUMO网络定义"]}
{"type":"entity","name":"车辆","entityType":"Vehicle","observations":["数据文件：工作日 (weekday.veh.xml.gz) 和周末 (weekend.veh.xml.gz)","车辆类型：私家车和出租车两种类型","V2G功能：支持Vehicle-to-Grid双向充放电","参与比例：V2G参与比例可配置","行为模式：工作日和周末不同的出行模式"]}
{"type":"entity","name":"充电站","entityType":"ChargingStation","observations":["ID格式：CS1到CS10的命名规范","充电桩数量：slots字段定义每站充电桩数量","功率单位：kW (千瓦) 为功率单位","最大功率：max_pc字段定义最大充电功率","功率分配：pc_alloc字段定义功率分配策略 (Average/Priority)","电价单位：元/kWh为电价计费单位","电价结构：pbuy节点定义分时电价","母线连接：bus字段定义连接的电网母线","交通连接：edge字段定义所在的交通道路边","地理坐标：x, y字段定义空间位置 (部分为inf表示未定义)"]}
{"type":"entity","name":"仿真参数","entityType":"SimulationParameter","observations":["时间单位：秒 (second) 为基本时间单位","仿真时长：START_TIME到END_TIME定义仿真周期","时间步长：TRAFFIC_STEP定义数据记录间隔","步长范围：1到3600秒的可配置范围","模式选择：IS_WEEKDAY布尔值控制工作日/周末模式","随机种子：RANDOM_SEED确保仿真结果可重现","输出控制：LOG_DATA_TYPES定义记录的数据类型","GUI设置：SHOW_GUI控制是否显示图形界面","路径算法：固定使用dijkstra算法"]}
{"type":"entity","name":"数据文件","entityType":"DataFile","observations":["充电站配置：37nodes.fcs.xml定义快充站参数","电网配置：37nodes.grid.xml定义电力系统参数","交通网络：37nodes.net.xml定义SUMO交通网络","插件配置：37nodes.plg.xml定义仿真插件","场景配置：37nodes.scs.xml定义仿真场景","SUMO配置：37nodes.sumocfg定义SUMO仿真参数","交通区域：37nodes.taz.xml定义交通分析区域","车辆数据：*.veh.xml.gz压缩格式的车辆行程文件","主要入口：main.py、config.py、gen_trip.py"]}
{"type":"entity","name":"充电决策系统","entityType":"系统模块","observations":["充电决策在多个时机启动：行程开始前、仿真步进中、行驶过程中、停车时","主要由ChargingDecisionManager类统一管理","包含快充决策、慢充决策、V2G决策三种类型"]}
{"type":"entity","name":"行程开始前充电决策","entityType":"决策时机","observations":["在__start_trip函数中实现","检查当前电量是否足够完成行程","支持基于距离和基于SOC阈值两种判断方式","如果电量不足会启动充电站选择流程"]}
{"type":"entity","name":"仿真步进充电决策","entityType":"决策时机","observations":["在simulation_step函数中实现","处理到达充电站的车辆，启动充电过程","监控行驶中车辆的电量状态"]}
{"type":"entity","name":"行驶过程充电决策","entityType":"决策时机","observations":["持续监控行驶中车辆的电量","当发现电量不足以到达目标充电站时触发","重新选择最近的可达充电站","如果找不到可达充电站，车辆会因电量耗尽而停止"]}
{"type":"entity","name":"双层步长架构","entityType":"系统架构","observations":["交通-电力耦合系统采用双层时间步长设计","SUMO内部步长处理微观交通行为，系统宏观步长处理充电决策","通过traci.simulationStep()函数协调两个步长","实现了微观精度和宏观效率的平衡"]}
{"type":"entity","name":"SUMO内部步长","entityType":"时间参数","observations":["在37nodes.sumocfg中设置为10秒","控制车辆移动、信号灯、碰撞检测等微观行为","影响交通仿真的精度和计算量","推荐范围：5-15秒"]}
{"type":"entity","name":"系统宏观步长","entityType":"时间参数","observations":["在config.py中设置为900秒(15分钟)","控制充电决策频率、数据记录间隔、电力系统更新","每个宏观步长内SUMO按内部步长运行多次","推荐范围：300-1800秒"]}
{"type":"entity","name":"步长协调机制","entityType":"系统机制","observations":["通过simulation_step函数实现协调","traci.simulationStep(current_time + step_len)让SUMO运行到目标时间","在宏观步长边界处理累积的交通事件","包括车辆到达/出发、充电站更新、充电决策等"]}
{"type":"entity","name":"充电负荷预测图","entityType":"可视化工具","observations":["文件名：plot_charging_load.py","功能：绘制电动汽车充电负荷随时间变化的预测图","数据源：bus.csv文件中的Time_hours和totPd列","横坐标：时间(小时)","纵坐标：总功率(MW)","输出格式：PNG图片文件","中文支持：标题、坐标轴标签全部使用中文","附加功能：趋势线分析、统计信息显示、峰谷值检测"]}
{"type":"entity","name":"绘图模块","entityType":"代码模块","observations":["目录位置：plots/","主要功能：生成交通-电力耦合系统的各种图表","核心模块：charging_load.py","说明文档：README.md"]}
{"type":"entity","name":"充电负荷绘图模块","entityType":"功能模块","observations":["文件名：plots/charging_load.py","主要函数：plot_charging_load(), analyze_charging_pattern()","数据源：fcs.csv(快充站)和scs.csv(慢充站)","输出格式：PNG图片(300 DPI)","显示内容：总充电负荷(红色)、快充负荷(蓝色)、慢充负荷(绿色)","单位转换：kW自MW自动转换","统计功能：峰谷分析、功率统计、占比计算","修复问题: 清理了多余的空行","修复问题: 确保数据文件路径正确","修复问题: 删除了未使用的参数警告","测试文件: test_charging_plot.py 和 verify_fix.py","新增功能: 数据文件选择配置系统","配置区域: DATA_CONFIG字典，包含多个预设数据源","便捷函数: plot_charging_load_auto() 自动使用配置的数据源","辅助函数: list_available_data_sources() 列出可用数据源","使用方法: 修改DATA_CONFIG中的'current'值来切换数据源","新增文件：plot_charging_analysis.py - 专门的充电分析绘图模块","主要函数：plot_charging_power() 绘制充电功率图，plot_charging_vehicles() 绘制车辆数图","组合函数：plot_charging_combined() 绘制双y轴综合图","数据处理：load_charging_data() 统一数据加载接口","时间处理：时间归一化为每天从0开始的24小时制","慢充功率计算：基于车辆数 × 3.3kW标准慢充功率","图表特性：中文标签、统计信息显示、高分辨率输出","输出文件：充电功率分析图.png、充电车辆数分析图.png、充电综合分析图.png"]}
{"type":"entity","name":"充电负荷图数据源","entityType":"数据文件","observations":["快充站数据文件：results/std_37nodes/csv_exports/fcs.csv","慢充站数据文件：results/std_37nodes/csv_exports/scs.csv","两个文件都包含Time_hours列作为时间轴（单位：小时）","数据格式为宽格式CSV，每行代表一个时间点的所有充电站数据"]}
{"type":"entity","name":"快充站数据结构","entityType":"数据格式","observations":["文件路径：results/std_37nodes/csv_exports/fcs.csv","Time_hours列：时间数据，单位为小时","以'#c'结尾的列：各快充站的充电功率，单位kW（如CS1#c, CS2#c等）","以'#cnt'结尾的列：各快充站的充电车辆数量","以'#pb'结尾的列：各快充站的电池功率","总快充功率 = 所有'#c'列的和"]}
{"type":"entity","name":"慢充站数据结构","entityType":"数据格式","observations":["文件路径：results/std_37nodes/csv_exports/scs.csv","Time_hours列：时间数据，单位为小时","以'#cnt'结尾的列：各慢充边的充电车辆数量（如edge10_1#cnt等）","慢充功率使用#cnt列数据，不是#c列","总慢充功率 = 所有'#cnt'列的和"]}
{"type":"entity","name":"充电负荷图绘制逻辑","entityType":"数据处理","observations":["代码文件：plots/charging_load.py","时间对齐：找到fcs.csv和scs.csv的共同时间点","快充功率计算：提取所有以'#c'结尾的列并求和","慢充功率计算：提取所有以'#cnt'结尾的列并求和","单位转换：从kW转换为MW显示（除以1000）","总充电负荷 = 快充功率 + 慢充功率"]}
{"type":"entity","name":"母线负荷数据结构","entityType":"数据格式","observations":["文件路径：results/std_37nodes/csv_exports/bus.csv","Time_hours列：时间数据，单位为小时","以'#Pd'结尾的列：各母线节点的有功负荷需求，单位MW（如b1#Pd, b10#Pd等）","以'#Pg'结尾的列：各母线节点的有功发电功率，单位MW","以'#Qd'结尾的列：各母线节点的无功负荷需求","以'#Qg'结尾的列：各母线节点的无功发电功率","以'#V'结尾的列：各母线节点的电压","totPd：总有功负荷需求","totPg：总有功发电功率"]}
{"type":"entity","name":"充电负荷与母线负荷关系","entityType":"系统架构","observations":["充电站通过node属性连接到对应的母线节点","充电负荷被加入到对应母线的Pd（有功负荷需求）中","代码位置：v2sim/plugins/pdn.py第72-78行","关键代码：self.__gr.Bus(b).Pd += v","母线总负荷 = 传统负荷 + 充电负荷","总充电负荷是母线总负荷的组成部分，不等于母线总负荷","母线负荷还包括居民用电、工业用电等其他传统负荷","bus.csv中的totPd = fcs.csv充电负荷 + scs.csv充电负荷 + 电网基础负荷","电网基础负荷包括居民、工业、商业等传统用电负荷","充电负荷通过代码动态加入到母线负荷中，实现电力-交通耦合","数值验证：同一时间点的充电功率总和 + 基础负荷 = totPd"]}
{"type":"entity","name":"行程参数文件夹","entityType":"配置目录","observations":["参数名：TRIP_PARAM_FOLDER","默认路径：v2sim/probtable","作用：存储电动汽车行程生成所需的概率分布参数","命令行参数：-c 参数指定自定义路径","空值行为：使用默认的v2sim/probtable目录"]}
{"type":"entity","name":"probtable目录结构","entityType":"数据目录","observations":["ev_types.csv：电动车类型配置（电池容量、续航里程、充电功率等）","vehicle_ratio.csv：车辆类别比例配置（私家车85%，出租车15%）","space_transfer_probability/：空间转移概率文件夹（H/W/R/O四种区域类型）","duration_of_parking/：停车时长概率分布文件夹","工作日/周末区分：每种参数都有weekday和weekend版本","区域类型：H(家庭)、W(工作)、R(休闲)、O(其他)四种交通分析区域"]}
{"type":"entity","name":"电动车类型配置","entityType":"参数文件","observations":["文件路径：probtable/ev_types.csv","字段定义：id(类型ID)、bcap_kWh(电池容量)、range_km(续航里程)","充电参数：efc_rate_kW(快充功率)、esc_rate_kW(慢充功率)、max_V2G_kW(V2G最大功率)","车辆分类：category字段区分private(私家车)和taxi(出租车)","数据格式：CSV格式，包含多种电动车型号的技术参数"]}
{"type":"entity","name":"OD生成模式","entityType":"行程生成参数","observations":["参数名：OD_GENERATION_MODE","OD含义：Origin-Destination（起点-终点对）","默认值：\"taz\"（基于TAZ的生成模式）","作用：控制电动汽车行程的起点和终点生成方式","命令行参数：-mode taz","支持模式：目前仅支持TAZ模式"]}
{"type":"entity","name":"TAZ交通分析区域","entityType":"空间分区","observations":["TAZ全称：Traffic Analysis Zone（交通分析区域）","配置文件：37nodes.taz.xml定义TAZ区域边界","类型文件：taz_type.txt定义TAZ类型分类","区域数量：总共37个TAZ区域（TAZ1到TAZ37）","空间定义：每个TAZ都有shape定义的地理边界","边缘关联：每个TAZ包含多个tazSource和tazSink边缘"]}
{"type":"entity","name":"TAZ区域类型分类","entityType":"出行模式","observations":["Home（家庭）：10个TAZ区域 - TAZ1,TAZ4,TAZ8,TAZ13,TAZ15,TAZ20,TAZ21,TAZ26,TAZ30,TAZ33","Work（工作）：9个TAZ区域 - TAZ2,TAZ6,TAZ9,TAZ14,TAZ17,TAZ22,TAZ25,TAZ31,TAZ35","Relax（休闲）：9个TAZ区域 - TAZ3,TAZ10,TAZ12,TAZ16,TAZ19,TAZ24,TAZ29,TAZ34,TAZ37","Other（其他）：9个TAZ区域 - TAZ5,TAZ7,TAZ11,TAZ18,TAZ23,TAZ27,TAZ28,TAZ32,TAZ36","出行链模式：H-W-H（家-工作-家）、H-W-R/O-H（家-工作-休闲/其他-家）","概率分布：基于空间转移概率和停车时长分布生成行程"]}
{"type":"entity","name":"CSV数据文件格式","entityType":"数据格式","observations":["快充站数据: results/std_37nodes/csv_exports/fcs.csv","慢充站数据: results/std_37nodes/csv_exports/scs.csv","时间列: Time_hours (小时为单位)","快充功率列: 以#c结尾的列名","慢充功率列: 以#cnt结尾的列名"]}
{"type":"entity","name":"v2sim/traffic/cs.py代码结构","entityType":"代码架构","observations":["文件长度：780行（从原始1046行减少25.4%）","主要类：CS抽象基类、SCS慢充站类、FCS快充站类","算法池：V2GAllocPool（V2G功率分配）、MaxPowerAllocPool（最大功率分配）","分配环境：AllocationEnvironment数据类","功率分配算法：平均分配、优先级分配、基于时间分配","核心属性：_current_charging_load、_current_discharge_load、_current_v2g_capacity","删除了所有向后兼容代码：AllocEnv、MaxPCAllocator、MaxPCAllocPool别名","删除了LoadFCS、LoadSCS包装函数","统一使用新的命名规范：AllocationEnvironment、MaxPowerAllocator等","保持了完整的充电站功能：充电、V2G、功率管理、状态管理"]}
{"type":"entity","name":"充电站代码简化过程","entityType":"重构记录","observations":["第一阶段：命名规范统一（_chi→_charging_vehicles，_cload→_current_charging_load等）","第二阶段：复杂方法拆分（SCS.update()拆分为_handle_charging()和_handle_v2g()）","第三阶段：文档简化（删除冗余注释和过详细的文档字符串）","第四阶段：删除向后兼容（彻底删除别名和包装函数）","修复了依赖问题：v2sim/statistics/logcs.py、v2sim/trafficgen/graph.py","测试验证：所有功能正常，主模块导入成功，充电负荷绘图正常","最终效果：代码减少266行，功能完全保持，接口统一化"]}
{"type":"entity","name":"充电站选择简化系统","entityType":"算法模块","observations":["核心文件：v2sim/traffic/cslist.py","主要类：CSList - 充电站列表管理","导入简化：从 feasytools 只导入 RangeList, Point","初始化逻辑：只创建 _remap 字典映射","核心方法：select_near() 返回前 n 个充电站索引","选择逻辑：如果 n >= 总数量则返回所有索引","实际筛选：由上层算法处理距离、在线状态等","性能优化：通过 max_nearby_stations 限制为10个","容错设计：不依赖坐标数据，系统稳定运行","功能状态：已删除KDTree相关代码，使用简化方案"]}
{"type":"entity","name":"充电站选择算法系统","entityType":"算法模块","observations":["核心文件：v2sim/traffic/charging_decision.py","主要类：ChargingStationSelector","智能选择算法：select_best_station() 综合考虑多因素","备用算法：get_nearest_station() 简单距离选择","考虑因素：行驶时间、等待时间、充电价格、时间偏好","权重公式：omega * (时间成本) + 电量 * 电价","车辆类型：支持私家车和出租车不同模式","时间模式：工作日/周末差异化处理","配置参数：max_nearby_stations=10 限制考虑数量","集成位置：在 TrafficInst 中调用"]}
{"type":"entity","name":"车辆文件管理简化系统","entityType":"文件管理","observations":["核心文件：v2sim/traffic/utils.py","简化逻辑：只支持工作日和周末两种模式","文件命名：.weekday. 和 .weekend. 标识符","数据结构：FileDetectResult 中 weekday_veh 和 weekend_veh 字段","检测逻辑：根据文件名自动分类","错误处理：重复文件检测和报错","通用文件：不包含模式标识的文件同时作为两种模式","删除内容：复杂的veh_files列表和多文件处理逻辑","优化效果：代码从50+行简化到不到20行","功能状态：完全满足项目需求，不再冗余"]}
{"type":"entity","name":"仿真车辆选择简化系统","entityType":"仿真管理","observations":["核心文件：v2sim/sim_core.py","简化方法：__get_vehicle_file() 替代 __select_vehicle_file()","选择逻辑：直接根据is_weekday参数选择文件","错误处理：找不到文件时直接抛出FileNotFoundError","日志输出：明确显示使用的文件类型","参数传递：从复杂的proj_cfg修改到直接返回文件路径","删除内容：复杂的文件搜索和向后兼容逻辑","代码减少：从34行简化到21行","逻辑清晰：直接明了的工作日/周末选择","性能提升：去除了不必要的循环和判断"]}
{"type":"relation","from":"充电站","to":"交通网络","relationType":"located_at"}
{"type":"relation","from":"发电机","to":"母线节点","relationType":"connected_to"}
{"type":"relation","from":"输电线路","to":"母线节点","relationType":"connects"}
{"type":"relation","from":"母线节点","to":"电网系统","relationType":"belongs_to"}
{"type":"relation","from":"车辆","to":"充电站","relationType":"uses"}
{"type":"relation","from":"车辆","to":"充电站","relationType":"充电负荷"}
{"type":"relation","from":"仿真参数","to":"电网系统","relationType":"控制"}
{"type":"relation","from":"仿真参数","to":"交通网络","relationType":"控制"}
{"type":"relation","from":"数据文件","to":"电网系统","relationType":"定义"}
{"type":"relation","from":"数据文件","to":"交通网络","relationType":"定义"}
{"type":"relation","from":"数据文件","to":"充电站","relationType":"定义"}
{"type":"relation","from":"输电线路","to":"电网系统","relationType":"part_of"}
{"type":"relation","from":"发电机","to":"电网系统","relationType":"part_of"}
{"type":"relation","from":"车辆","to":"交通网络","relationType":"operates_in"}
{"type":"relation","from":"充电决策系统","to":"行程开始前充电决策","relationType":"包含"}
{"type":"relation","from":"充电决策系统","to":"仿真步进充电决策","relationType":"包含"}
{"type":"relation","from":"充电决策系统","to":"行驶过程充电决策","relationType":"包含"}
{"type":"relation","from":"行程开始前充电决策","to":"仿真步进充电决策","relationType":"时序先于"}
{"type":"relation","from":"仿真步进充电决策","to":"行驶过程充电决策","relationType":"并行执行"}
{"type":"relation","from":"双层步长架构","to":"SUMO内部步长","relationType":"包含"}
{"type":"relation","from":"双层步长架构","to":"系统宏观步长","relationType":"包含"}
{"type":"relation","from":"双层步长架构","to":"步长协调机制","relationType":"依赖"}
{"type":"relation","from":"步长协调机制","to":"SUMO内部步长","relationType":"协调"}
{"type":"relation","from":"步长协调机制","to":"系统宏观步长","relationType":"协调"}
{"type":"relation","from":"充电决策系统","to":"系统宏观步长","relationType":"受控于"}
{"type":"relation","from":"充电站","to":"母线节点","relationType":"connected_to"}
{"type":"relation","from":"充电负荷预测图","to":"数据文件","relationType":"使用"}
{"type":"relation","from":"充电负荷预测图","to":"母线节点","relationType":"分析"}
{"type":"relation","from":"绘图模块","to":"充电负荷绘图模块","relationType":"包含"}
{"type":"relation","from":"充电负荷绘图模块","to":"数据文件","relationType":"使用"}