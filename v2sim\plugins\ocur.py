from collections import defaultdict
from itertools import chain

from ..traffic.cs import CS
from fpowerkit import IslandResult
from .pdn import PluginPDN
from .base import *

class PluginOvercurrent(PluginBase[None]):
    @property
    def Description(self)->str:
        return "过流保护"


    @staticmethod
    def ElemShouldHave() -> ConfigDict:
        '''Get the plugin configuration item list'''
        return ConfigDict()
    
    def Init(self,elem:ET.Element,inst:TrafficInst,work_dir:Path,res_dir:Path,plg_deps:'list[PluginBase]')->None:
        self.__file = open(str(res_dir / "current_protect.log"), "w")
        self.SetPreStep(self._work)
        self.SetPostSimulation(self.__file.close)
        assert len(plg_deps) == 1 and isinstance(plg_deps[0], PluginPDN), "过流保护依赖于PDN插件"
        self.__pdn = plg_deps[0]
        if self.__pdn.isSmartChargeEnabled():
            raise RuntimeError("启用有序充电时过流保护不可用")
        self.__csatb:dict[str, list[CS]] = defaultdict(list)
        for cs in chain(inst.SCSList, inst.FCSList):
            self.__csatb[cs.node].append(cs)
        self.__csatb_closed:dict[str, bool] = {
            b:False for b in self.__csatb
        }
    
    def _close(self, b:str):
        '''
        Force shutdown all charging stations at bus b
        '''
        if self.__csatb_closed[b]: return
        for cs in self.__csatb[b]:
            cs.force_shutdown()
        print(f"CS {','.join(map(lambda x:x.name, self.__csatb[b]))} forced shutdown at bus {b}.", file=self.__file)

    def _work(self,_t:int,/,sta:PluginStatus)->tuple[bool, None]:
        '''
        Get the V2G demand power of all bus with slow charging stations at time _t, unit kWh/s, 3.6MW=3600kW=1kWh/s
        '''
        
        if sta == PluginStatus.EXECUTE:
            p = self.__pdn
            if p.LastPreStepSucceed:
                svr = p.Solver
                if len(svr.est.OverflowLines) > 0:
                    print(f"[{_t}] Overcurrent protection triggered: ", svr.est.OverflowLines, file=self.__file)
                    for l in svr.est.OverflowLines:
                        ln = p.Grid.Line(l)
                        ln.P = 0.
                        ln.Q = 0.
                        ln.I = 0.
                    svr.est.UpdateGrid(cut_overflow_lines=True)
                    self.__island_closed = [False] * len(svr.est.Islands)
                    svr.solve(_t)
                for i, (il, (res, val)) in enumerate(zip(svr.est.Islands, svr.est.IslandResults)):
                    if res == IslandResult.Failed and not self.__island_closed[i]:
                        self.__island_closed[i] = True
                        for b in il.Buses: self._close(b)
                ret = True, None
            else:
                ret = False, None
        elif sta == PluginStatus.OFFLINE:
            ret = True, None
        elif sta == PluginStatus.HOLD:
            ret = True, self.LastPreStepResult
        return ret