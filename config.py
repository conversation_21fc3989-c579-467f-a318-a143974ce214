# 配置文件夹路径
CONFIG_DIR = "cases/std_37nodes"

# 输出文件夹
OUTPUT_DIR = "results"

# 仿真时间设置
START_TIME = 0          # 开始时间(秒)
END_TIME = 172800       # 结束时间(秒) - 48小时
TRAFFIC_STEP = 900      # 数据记录步长(秒)

# 随机种子
RANDOM_SEED = 12345     # 用于可重复的仿真结果

# 工作日/周末模式配置
IS_WEEKDAY = True       # True: 工作日模式, False: 周末模式

# 记录的数据类型
LOG_DATA_TYPES = [
    'fcs',          # 快充站数据
    'scs',          # 慢充站数据
    'ev',           # 电动车数据
    'ev_by_type',   # 按车辆类型分类的电动车数据 (私家车/出租车)
]

# 可视化设置
SHOW_GUI = False                # 是否显示SUMO图形界面
NO_DAEMON = False               # 是否分离仿真线程与显示窗口
DEBUG_MODE = False              # 是否启用图形仿真调试模式

# 路径算法 (固定使用dijkstra)
ROUTE_ALGORITHM = "dijkstra"

# 文件管理
COPY_CONFIG = False             # 仿真结束后复制配置文件
CSV_EXPORT = True               # 启用CSV标准化导出

# 禁用的插件列表
DISABLED_PLUGINS = [
]

def get_simulation_config():
    """获取完整的仿真配置字典"""
    import os
    if not os.path.exists(CONFIG_DIR):
        print(f"配置错误: 配置文件夹不存在: {CONFIG_DIR}")
        raise ValueError("配置验证失败")

    return {
        'cfgdir': CONFIG_DIR,
        'outdir': OUTPUT_DIR,
        'start_time': START_TIME,
        'end_time': END_TIME,
        'traffic_step': TRAFFIC_STEP,
        'seed': RANDOM_SEED,
        'log': ",".join(LOG_DATA_TYPES),
        'copy': COPY_CONFIG,
        'csv_export': CSV_EXPORT,
        'route_algo': ROUTE_ALGORITHM,
        'no_plg': ",".join(DISABLED_PLUGINS) if DISABLED_PLUGINS else "",
        'show': SHOW_GUI,
        'no_daemon': NO_DAEMON,
        'debug': DEBUG_MODE,
        'is_weekday': IS_WEEKDAY,
    }
