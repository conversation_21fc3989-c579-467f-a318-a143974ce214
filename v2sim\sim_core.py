import importlib, os, shutil, signal, time, sys
from typing import Any, Optional, Union
from feasytools import time2str
from pathlib import Path

from .plugins import *
from .statistics import *
from .traffic import *

from .trafficgen import TrafficGenerator, RoadNetConnectivityChecker
from .csv_export import CSVExportManager
from .traffic.inst import traci



def load_external_components(
    external_plugin_dir: str, plugin_pool: PluginPool, sta_pool: StaPool
):
    exp = Path(os.getcwd()) / Path(external_plugin_dir)
    if not (exp.exists() and exp.is_dir()):
        return
    for module_file in exp.iterdir():
        if not (
            module_file.is_file()
            and module_file.suffix == ".py"
            and not module_file.name.startswith("_")
        ):
            continue
        module_name = module_file.stem
        try:
            module = importlib.import_module(f"{external_plugin_dir}.{module_name}")
        except Exception as e:
            print(f"警告: {module_name}是Python文件, 但无法作为包加载: {e}")
            module = None
        if hasattr(module, "plugin_exports"):
            try:
                plugin_pool._Register(*module.plugin_exports) # type: ignore
            except Exception as e:
                print(f"警告: {module_name}的plugin_exports无效, 无法作为插件导入: {e}")
        if hasattr(module, "sta_exports"):
            try:
                sta_pool.Register(*module.sta_exports) # type: ignore
            except Exception as e:
                print(f"警告: {module_name}的sta_exports无效, 无法作为统计项导入: {e}")


class V2SimInstance:

    
    def __print(self, con:str="", *, file:Any = sys.stdout, end="\n"):
        if not self.__silent:
            print(con, file=file, end=end)

    def __init__(
        self,
        cfgdir: str,
        outdir: str = "./results",
        *,
        outdir_direct: str = "",
        plg_pool: Optional[PluginPool] = None,
        sta_pool: Optional[StaPool] = None,
        is_weekday: bool = True,  # 工作日/周末模式配置
        traffic_step: int = 10,
        start_time: int = 0,
        end_time: int = 172800,
        no_plg: str = "",
        log: str = "fcs, scs",
        seed: int = 0,
        copy: bool = False,
        silent: bool = False,
        csv_export: bool = True,
        route_algo: str = "dijkstra",
        show: bool = False,
        no_daemon: bool = False,
        debug: bool = False,
    ):
        '''
        Initialization
            cfgdir: Configuration folder
            outdir: Output folder. Actual results will be saved in a subfolder named by the configuration folder
            outdir_direct: Direct output folder
            plg_pool: Available plugin pool
            sta_pool: Available statistical item pool
            traffic_step: Simulation step
            start_time: Start time
            end_time: End time
            no_plg: Disabled plugins, separated by commas
            log: Data to be recorded, separated by commas
            seed: Randomization seed
            copy: Whether to copy the configuration file after the simulation ends

            silent: Whether to silent mode, default is False
            route_algo: SUMO Routing algorithm, fixed to dijkstra
        '''

        if plg_pool is None: plg_pool = PluginPool()
        if sta_pool is None: sta_pool = StaPool()

        self.__silent = silent
        self._csv_export_enabled = csv_export

        # Check if there is a previous results
        if outdir_direct != "":
            pres = Path(outdir_direct)
        else:
            pres = Path(outdir) / Path(cfgdir).name
        self.__outdir_direct = str(pres)
        self.__outdir = str(pres.parent)
        if pres.is_dir() and (pres / "cproc.clog").exists():
            tm = time.strftime("%Y%m%d_%H%M%S", time.localtime(pres.stat().st_mtime))
            tm2 = time.time_ns() % int(1e9)
            new_path = f"{str(pres)}_{tm}_{tm2}"
            pres.rename(new_path)
        pres.mkdir(parents=True, exist_ok=True)
        self.__pres = pres

        # Create cproc.log
        self.__out = open(str(pres / "cproc.log"), "w", encoding="utf-8")

        proj_dir = Path(cfgdir)

        # 车辆生成已通过gen_trip.py独立完成，不再需要运行时生成
        vehicles = None

        proj_cfg = DetectFiles(str(proj_dir))

        # 根据工作日/周末配置选择车辆文件
        veh_file = self.__get_vehicle_file(proj_cfg, is_weekday)

        if proj_cfg.py:
            with open(proj_cfg.py,"r",encoding="utf-8") as f:
                code = f.read()
                exec(code)
            
        # Detect SUMO configuration
        if not proj_cfg.cfg:
            raise FileNotFoundError("错误: 未指定SUMO配置文件.")
        sumocfg_file = proj_cfg.cfg
        _stt, _edt, _rnet = GetTimeAndNetwork(sumocfg_file)
        self.__print(f"  SUMO: {sumocfg_file}")

        # Detect road network file
        if _rnet is None:
            if not proj_cfg.net:
                raise RuntimeError("错误: 未指定路网文件.")
            else:
                rnet_file = proj_cfg.net
        else:
            rnet_file = proj_dir / _rnet
            if rnet_file.exists():
                rnet_file = str(rnet_file)
            else:
                raise FileNotFoundError("错误: 未指定路网文件.")
        elg = RoadNetConnectivityChecker(rnet_file)
        elg.checkBadCS()
        self.__print(f"  路网: {rnet_file}")
        
        # Check vehicles and trips
        if not veh_file:
            raise FileNotFoundError("错误: 未找到EV和行程文件.")
        if vehicles is None:
            vehicles = EVDict(veh_file)
        self.__print(f"  行程: {veh_file}, {len(vehicles)}辆EV")

        # Check FCS file
        if not proj_cfg.fcs:
            raise FileNotFoundError("错误: 未找到快充站文件.")
        fcs_file = proj_cfg.fcs
        fcs_obj:CSList[FCS] = CSList(vehicles, filePath = fcs_file, csType = FCS)
        self.__print(f"  快充: {fcs_file}, {len(fcs_obj)}个站点")

        # Check SCS file
        if not proj_cfg.scs:
            raise FileNotFoundError("错误: 未找到慢充站文件.")
        scs_file = proj_cfg.scs
        scs_obj:CSList[SCS] = CSList(vehicles, filePath = scs_file, csType = SCS)
        self.__print(f"  慢充: {scs_file}, {len(scs_obj)}个站点")

        # Check start and end time
        if start_time == -1:
            start_time = _stt
        if end_time == -1:
            end_time = _edt
        if start_time == -1 or end_time == -1:
            raise ValueError("错误: 仿真起止时间未指定.")
        self.__start_time = start_time
        self.__end_time = end_time
        self.__sim_dur = end_time - start_time
        self.__print(f"  起止时间: {start_time} ~ {end_time}, 步长：{traffic_step}")

        # Create a simulation instance
        self.__inst = TrafficInst(
            rnet_file, start_time, traffic_step, 
            end_time, str(pres / "cproc.clog"), seed,
            vehfile = veh_file, veh_obj = vehicles,
            fcsfile = fcs_file, fcs_obj = fcs_obj,
            scsfile = scs_file, scs_obj = scs_obj,

            routing_algo = route_algo,
        )

        # Enable plugins
        self.__gridplg = None
        if proj_cfg.plg:
            plg_file = proj_cfg.plg
            plg_man = PluginMan(
                str(plg_file), 
                pres,
                self.__inst,
                list(map(lambda x: x.strip().lower(), no_plg.split(","))),
                plg_pool
            )
            for plugname, plugin in plg_man.GetPlugins().items():
                if isinstance(plugin, PluginPDN):
                    self.__gridplg = plugin
                self.__print(f"  插件: {plugname} - {plugin.Description}")
        else:
            plg_man = PluginMan(None, pres, self.__inst, [], plg_pool)

        # Create a data logger
        log_item = log.strip().lower().split(",")
        if len(log_item) == 1 and log_item[0] == "": log_item = []
        mySta = StaWriter(str(pres), self.__inst, plg_man.GetPlugins(), sta_pool)
        for itm in log_item:
            mySta.Add(itm)
        
        self.__sta = mySta
        self.__plgman = plg_man
        self.__steplen = traffic_step
        self.__sumocfg_file = sumocfg_file
        self.__rnet_file = rnet_file
        self.__copy = copy

        self.__veh_file = veh_file
        self.__fcs_file = fcs_file
        self.__scs_file = scs_file
        self.__plg_file = plg_file
        self.__proj_cfg = proj_cfg
        self.__proj_dir = proj_dir
        self.__working_flag = False
        self.routing_algo = route_algo
        self.__show_gui = show
        self.__no_daemon = no_daemon
        self.__debug_mode = debug



    @property
    def project_dir(self):
        '''Folder of the project'''
        return self.__proj_dir
    
    @property
    def result_dir(self):
        '''Folder of results'''
        return self.__outdir
    
    @property
    def result_dir_direct(self):
        '''Direct output folder'''
        return self.__outdir_direct
    

    
    @property
    def ctime(self):
        '''Current simulation time, in second'''
        return self.__inst.current_time
    
    @property
    def step_length(self):
        '''Step length, in second'''
        return self.__steplen
    
    @property
    def btime(self):
        '''Simulation start time, in second'''
        return self.__start_time
    
    @property
    def etime(self):
        '''Simulation end time, in second'''
        return self.__end_time
    
    @property
    def copy(self):
        '''Indicate whether copy the source after simulation'''
        return self.__copy
    
    @property
    def clientID(self):
        '''Client ID in multiprocessing simulation'''
        return self.__clntID
    
    @property
    def silent(self):
        '''Indicate whether disable output'''
        return self.__silent
    
    @property
    def files(self):
        '''Files in the project'''
        return self.__proj_cfg
    
    @property
    def plugins(self):
        '''Plugins in the project'''
        return self.__plgman
    
    @property
    def statistics(self):
        '''Statistics in the project'''
        return self.__sta
    
    @property
    def core(self):
        '''Simulation core'''
        return self.__inst
    
    @property
    def fcs(self):
        '''List of FCSs'''
        return self.__inst.FCSList
    
    @property
    def scs(self):
        '''List of SCSs'''
        return self.__inst.SCSList
    
    @property
    def vehicles(self):
        '''Dict of vehicles'''
        return self.__inst.vehicles
    
    @property
    def edges(self):
        '''List of the edges'''
        return self.__inst.edges
    
    @property
    def edge_names(self):
        '''Name list of the edges'''
        return self.__inst.get_edge_names()
    
    @property
    def veh_count(self):
        '''Number of vehicles'''
        return len(self.__inst.vehicles)
    
    @property
    def is_working(self):
        '''Determine whether the simulation has started'''
        return self.__working_flag
    
    @property
    def pdn(self) -> Optional[PluginPDN]:
        '''Power grid plugin'''
        return self.__gridplg
    

    
    def start(self):
        '''
        Start simulation.
            If you use this function, do not use function 'simulation'.
            Follow the start - step - stop paradigm.
        '''
        self.__working_flag = True
        self.__inst.simulation_start(self.__sumocfg_file, self.__rnet_file, self.__start_time, self.__show_gui)
        self.__plgman.PreSimulationAll()
    
    def step(self) -> int:
        '''
        Simulation steps. 
            If you use this function, do not use function 'simulation'.
            Follow the start - step - stop paradigm.
        Return the simulation time after this step.
        '''
        t = self.__inst.current_time
        self.__plgman.PreStepAll(t)
        self.__inst.simulation_step(self.__steplen)
        self.__plgman.PostStepAll(t)
        self.__sta.Log(t)
        return self.__inst.current_time
    
    def step_until(self, t:int) -> int:
        '''
        Simulation steps till time t. 
            If you use this function, do not use function 'simulation'.
            Follow the start - step - stop paradigm.
        Return the simulation time after stepping.
        '''
        while self.__inst.current_time < t:
            self.step()
        return self.__inst.current_time
    

    
    def stop(self):
        '''
        Stop simulation.
            If you use this function, do not use function 'simulation'.
            Follow the start - step - stop paradigm.
        '''
        self.__plgman.PostSimulationAll()
        self.__inst.simulation_stop()
        self.__sta.close()
        self.__out.close()

        # CSV标准化导出
        if hasattr(self, '_csv_export_enabled') and self._csv_export_enabled:
            try:
                csv_exporter = CSVExportManager(str(self.__pres), str(self.__pres), enabled=True)
                csv_exporter.export_all()
            except Exception as e:
                print(f"CSV导出失败: {e}")

        if self.__copy:
            shutil.copy(self.__veh_file, self.__pres / Path(self.__veh_file).name)
            shutil.copy(self.__fcs_file, self.__pres / Path(self.__fcs_file).name)
            shutil.copy(self.__scs_file, self.__pres / Path(self.__scs_file).name)
            shutil.copy(self.__plg_file, self.__pres / Path(self.__plg_file).name)
            shutil.copy(self.__sumocfg_file, self.__pres / Path(self.__sumocfg_file).name)
        self.__working_flag = False
    
    def simulate(self):
        '''
        Main simulation function
            If you use this function, do not use start - step - stop paradigm
        Returns:
            (Whether the simulation ends normally, TrafficInst instance, StaWriter instance)
        '''
        self.__stopsig = False

        def eh(signum, frame):
            self.__print()
            self.__print("收到Ctrl-C退出信号, 提前退出")

            self.__stopsig = True
        
        signal.signal(signal.SIGINT, eh)
        
        self.__st_time = time.time()
        self.__last_print_time = 0


        self.start()

        while self.__inst.current_time < self.__end_time:
            try:
                self.step()
            except traci.FatalTraCIError as e:
                self.__stopsig = True
            if self.__stopsig:
                break
            self._istep()
        
        dur = time.time() - self.__st_time
        print(f"仿真结束. 用时: {time2str(dur)}",file=self.__out)
        self.__out.close()
        self.stop()
        self.__print()
        self.__print(f"仿真结束. 用时: {time2str(dur)}")



        return not self.__stopsig, self.__inst, self.__sta

    def _istep(self):
        # Command line progress display
        ctime = time.time()
        if ctime - self.__last_print_time > 1 or self.__inst.current_time >= self.__end_time:
            # Progress in command line updates once per second
            progress = 100 * (self.__inst.current_time - self.__start_time) / self.__sim_dur
            eta = (
                time2str((ctime - self.__st_time) * (100 - progress) / progress)
                if ctime - self.__st_time > 3
                else "N/A"
            )
            self.__print("\r",end="")
            self.__print(
                f"进度: {round(progress,2):.2f}%, {self.__inst.current_time}/{self.__end_time}. 已用时: {time2str(ctime-self.__st_time)}, 预计剩余时间: {eta}",
                end="",
            )
            self.__last_print_time = ctime

    def __get_vehicle_file(self, proj_cfg: FileDetectResult, is_weekday: bool) -> str:
        """
        根据工作日/周末配置获取车辆文件
        Args:
            proj_cfg: 文件检测结果
            is_weekday: 是否为工作日模式
        Returns:
            车辆文件路径
        """
        if is_weekday:
            if proj_cfg.weekday_veh:
                self.__print(f"使用工作日车辆文件: {proj_cfg.weekday_veh}")
                return proj_cfg.weekday_veh
            else:
                raise FileNotFoundError("错误: 未找到工作日车辆文件")
        else:
            if proj_cfg.weekend_veh:
                self.__print(f"使用周末车辆文件: {proj_cfg.weekend_veh}")
                return proj_cfg.weekend_veh
            else:
                raise FileNotFoundError("错误: 未找到周末车辆文件")

def simulate_single(**kwargs)->bool:
    '''
    Single process simulation
        kwargs: Simulation parameters. Use function 'get_sim_params' to get.
    '''
    return V2SimInstance(**kwargs, silent=False).simulate()[0]



if __name__ == "__main__":
    print("这是仿真系统的核心模块。不要直接运行此文件。请改用main.py。")