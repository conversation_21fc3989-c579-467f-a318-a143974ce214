#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
充电分析绘图模块
功能：绘制充电总功率和充电车辆数随时间变化的图表
作者：交通-电力耦合网络研究团队
"""

import pandas as pd
import matplotlib.pyplot as plt
import matplotlib.font_manager as fm
import numpy as np
from pathlib import Path
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

def load_charging_data(fcs_file='results/std_37nodes/csv_exports/fcs.csv',
                      scs_file='results/std_37nodes/csv_exports/scs.csv'):
    """
    加载充电站数据

    Args:
        fcs_file: 快充站数据文件路径
        scs_file: 慢充站数据文件路径

    Returns:
        tuple: (时间数组, 快充功率, 慢充功率, 快充车辆数, 慢充车辆数)
    """
    # 读取快充站数据
    fcs_data = pd.read_csv(fcs_file)

    # 读取慢充站数据
    scs_data = pd.read_csv(scs_file)

    # 找到两个数据集的共同时间点
    common_times = pd.merge(fcs_data[['Time', 'Time_hours']],
                           scs_data[['Time', 'Time_hours']],
                           on=['Time', 'Time_hours'], how='inner')

    # 根据共同时间点筛选数据
    fcs_filtered = pd.merge(common_times, fcs_data, on=['Time', 'Time_hours'], how='left')
    scs_filtered = pd.merge(common_times, scs_data, on=['Time', 'Time_hours'], how='left')

    # 提取时间数据（转换为从0开始的小时数）
    time_hours = fcs_filtered['Time_hours'].values
    # 将时间归一化为每天从0开始
    time_normalized = time_hours % 24

    # 计算快充功率（所有以#c结尾的列）
    fcs_power_cols = [col for col in fcs_filtered.columns if col.endswith('#c')]
    fast_charge_power = fcs_filtered[fcs_power_cols].sum(axis=1).values

    # 计算快充车辆数（所有以#cnt结尾的列）
    fcs_count_cols = [col for col in fcs_filtered.columns if col.endswith('#cnt')]
    fast_charge_vehicles = fcs_filtered[fcs_count_cols].sum(axis=1).values

    # 计算慢充车辆数（所有以#cnt结尾的列）
    scs_count_cols = [col for col in scs_filtered.columns if col.endswith('#cnt')]
    slow_charge_vehicles = scs_filtered[scs_count_cols].sum(axis=1).values

    # 慢充功率基于车辆数计算，使用标准慢充功率3.3kW
    slow_charge_power = slow_charge_vehicles * 3.3

    return time_normalized, fast_charge_power, slow_charge_power, fast_charge_vehicles, slow_charge_vehicles

def plot_charging_power(save_path='充电功率分析图.png', figsize=(12, 8)):
    """
    绘制充电总功率随时间变化图
    
    Args:
        save_path: 图片保存路径
        figsize: 图片尺寸
    """
    # 加载数据
    time_hours, fast_power, slow_power, _, _ = load_charging_data()
    
    # 计算总功率
    total_power = fast_power + slow_power
    
    # 创建图形
    fig, ax = plt.subplots(figsize=figsize)
    
    # 绘制功率曲线
    ax.plot(time_hours, total_power/1000, 'r-', linewidth=2.5, label='总充电功率', alpha=0.8)
    ax.plot(time_hours, fast_power/1000, 'b--', linewidth=2, label='快充功率', alpha=0.7)
    ax.plot(time_hours, slow_power/1000, 'g:', linewidth=2, label='慢充功率', alpha=0.7)
    
    # 设置图表属性
    ax.set_xlabel('时间 (小时)', fontsize=14, fontweight='bold')
    ax.set_ylabel('充电功率 (MW)', fontsize=14, fontweight='bold')
    ax.set_title('电动汽车充电功率随时间变化', fontsize=16, fontweight='bold', pad=20)
    
    # 设置坐标轴
    ax.set_xlim(0, 24)
    ax.set_xticks(range(0, 25, 2))
    ax.grid(True, alpha=0.3, linestyle='-', linewidth=0.5)
    
    # 添加图例
    ax.legend(loc='upper right', fontsize=12, framealpha=0.9)
    
    # 添加统计信息
    max_power = np.max(total_power/1000)
    max_time = time_hours[np.argmax(total_power)]
    ax.text(0.02, 0.98, f'峰值功率: {max_power:.2f} MW\n峰值时间: {max_time:.1f}小时', 
            transform=ax.transAxes, fontsize=11, verticalalignment='top',
            bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))
    
    # 保存图片
    plt.tight_layout()
    plt.savefig(save_path, dpi=300, bbox_inches='tight', facecolor='white')
    plt.show()
    
    print(f"充电功率分析图已保存至: {save_path}")

def plot_charging_vehicles(save_path='充电车辆数分析图.png', figsize=(12, 8)):
    """
    绘制充电车辆数随时间变化图
    
    Args:
        save_path: 图片保存路径
        figsize: 图片尺寸
    """
    # 加载数据
    time_hours, _, _, fast_vehicles, slow_vehicles = load_charging_data()
    
    # 计算总车辆数
    total_vehicles = fast_vehicles + slow_vehicles
    
    # 创建图形
    fig, ax = plt.subplots(figsize=figsize)
    
    # 绘制车辆数曲线
    ax.plot(time_hours, total_vehicles, 'r-', linewidth=2.5, label='总充电车辆数', alpha=0.8)
    ax.plot(time_hours, fast_vehicles, 'b--', linewidth=2, label='快充车辆数', alpha=0.7)
    ax.plot(time_hours, slow_vehicles, 'g:', linewidth=2, label='慢充车辆数', alpha=0.7)
    
    # 设置图表属性
    ax.set_xlabel('时间 (小时)', fontsize=14, fontweight='bold')
    ax.set_ylabel('充电车辆数 (辆)', fontsize=14, fontweight='bold')
    ax.set_title('电动汽车充电车辆数随时间变化', fontsize=16, fontweight='bold', pad=20)
    
    # 设置坐标轴
    ax.set_xlim(0, 24)
    ax.set_xticks(range(0, 25, 2))
    ax.grid(True, alpha=0.3, linestyle='-', linewidth=0.5)
    
    # 添加图例
    ax.legend(loc='upper right', fontsize=12, framealpha=0.9)
    
    # 添加统计信息
    max_vehicles = np.max(total_vehicles)
    max_time = time_hours[np.argmax(total_vehicles)]
    ax.text(0.02, 0.98, f'峰值车辆数: {max_vehicles:.0f} 辆\n峰值时间: {max_time:.1f}小时', 
            transform=ax.transAxes, fontsize=11, verticalalignment='top',
            bbox=dict(boxstyle='round', facecolor='lightblue', alpha=0.8))
    
    # 保存图片
    plt.tight_layout()
    plt.savefig(save_path, dpi=300, bbox_inches='tight', facecolor='white')
    plt.show()
    
    print(f"充电车辆数分析图已保存至: {save_path}")

def plot_charging_combined(save_path='充电综合分析图.png', figsize=(15, 10)):
    """
    绘制充电功率和车辆数的组合图（双y轴）
    
    Args:
        save_path: 图片保存路径
        figsize: 图片尺寸
    """
    # 加载数据
    time_hours, fast_power, slow_power, fast_vehicles, slow_vehicles = load_charging_data()
    
    # 计算总量
    total_power = fast_power + slow_power
    total_vehicles = fast_vehicles + slow_vehicles
    
    # 创建图形和双y轴
    fig, ax1 = plt.subplots(figsize=figsize)
    ax2 = ax1.twinx()
    
    # 绘制功率曲线（左y轴）
    line1 = ax1.plot(time_hours, total_power/1000, 'r-', linewidth=3, label='总充电功率', alpha=0.8)
    line2 = ax1.plot(time_hours, fast_power/1000, 'b--', linewidth=2, label='快充功率', alpha=0.7)
    line3 = ax1.plot(time_hours, slow_power/1000, 'g:', linewidth=2, label='慢充功率', alpha=0.7)
    
    # 绘制车辆数曲线（右y轴）
    line4 = ax2.plot(time_hours, total_vehicles, 'orange', linewidth=2.5, label='总充电车辆数', alpha=0.8)
    
    # 设置左y轴（功率）
    ax1.set_xlabel('时间 (小时)', fontsize=14, fontweight='bold')
    ax1.set_ylabel('充电功率 (MW)', fontsize=14, fontweight='bold', color='red')
    ax1.tick_params(axis='y', labelcolor='red')
    ax1.set_xlim(0, 24)
    ax1.set_xticks(range(0, 25, 2))
    ax1.grid(True, alpha=0.3, linestyle='-', linewidth=0.5)
    
    # 设置右y轴（车辆数）
    ax2.set_ylabel('充电车辆数 (辆)', fontsize=14, fontweight='bold', color='orange')
    ax2.tick_params(axis='y', labelcolor='orange')
    
    # 设置标题
    ax1.set_title('电动汽车充电功率与车辆数随时间变化', fontsize=16, fontweight='bold', pad=20)
    
    # 合并图例
    lines = line1 + line2 + line3 + line4
    labels = [l.get_label() for l in lines]
    ax1.legend(lines, labels, loc='upper left', fontsize=12, framealpha=0.9)
    
    # 添加统计信息
    max_power = np.max(total_power/1000)
    max_vehicles = np.max(total_vehicles)
    stats_text = f'峰值功率: {max_power:.2f} MW\n峰值车辆数: {max_vehicles:.0f} 辆'
    ax1.text(0.02, 0.98, stats_text, transform=ax1.transAxes, fontsize=11, 
             verticalalignment='top', bbox=dict(boxstyle='round', facecolor='lightyellow', alpha=0.8))
    
    # 保存图片
    plt.tight_layout()
    plt.savefig(save_path, dpi=300, bbox_inches='tight', facecolor='white')
    plt.show()
    
    print(f"充电综合分析图已保存至: {save_path}")

def plot_all_charging_analysis():
    """
    生成所有充电分析图表
    """
    print("开始生成充电分析图表...")
    
    # 检查数据文件是否存在
    fcs_file = Path('results/std_37nodes/csv_exports/fcs.csv')
    scs_file = Path('results/std_37nodes/csv_exports/scs.csv')
    
    if not fcs_file.exists():
        print(f"错误：找不到快充站数据文件 {fcs_file}")
        return
    
    if not scs_file.exists():
        print(f"错误：找不到慢充站数据文件 {scs_file}")
        return
    
    # 生成各种图表
    plot_charging_power()
    plot_charging_vehicles()
    plot_charging_combined()
    
    print("所有充电分析图表生成完成！")

if __name__ == "__main__":
    plot_all_charging_analysis()
