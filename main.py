import sys
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

from v2sim import PluginPool, StaPool, load_external_components, simulate_single


def load_config_from_file() -> dict:
    """动态加载config.py配置文件"""
    import importlib.util
    spec = importlib.util.spec_from_file_location("config", "config.py")
    config_module = importlib.util.module_from_spec(spec)
    spec.loader.exec_module(config_module)
    return config_module.get_simulation_config()


def main():
    """交通-电力耦合系统仿真主程序"""
    # 初始化插件池和统计池
    sta_pool = StaPool()
    plg_pool = PluginPool()

    # 加载外部扩展组件
    if Path("external_components").exists():
        load_external_components("external_components", plg_pool, sta_pool)

    # 加载配置并启动仿真
    kwargs = load_config_from_file()
    kwargs.update({'plg_pool': plg_pool, 'sta_pool': sta_pool})

    simulate_single(**kwargs)


if __name__ == "__main__":
    main()
