"""
V2SIM充电决策模块

该模块封装了电动汽车充电决策的核心逻辑，包括：
- 充电时间偏好管理
- 充电意愿评估
- 充电站选择算法
- 充电决策管理

作者：V2SIM团队
"""

from __future__ import annotations
import enum
import math
import random
from typing import Callable, Optional, Tuple, Dict, Any, TYPE_CHECKING
import numpy as np
from feasytools import RangeList

if TYPE_CHECKING:
    from .ev import EV, VehStatus
    from .trip import Trip
    from .cslist import CSList
    from .cs import FCS
    from .utils import Point, Stage

# 充电决策配置 - 支持工作日/周末差异化配置
CHARGING_CONFIG = {
    'time_preference': {
        'private_car': {
            'weekday': {
                'type': 'asymmetric_double_peak',  # 非对称双峰：早间小峰 + 晚间主峰
                'peak1_time': 420,    # 7:00 (早间小峰)
                'peak1_weight': 0.1,  # 早间峰值权重
                'peak1_std': 120,     # 早间峰标准差2小时
                'peak2_time': 1230,   # 20:30 (晚间主峰)
                'peak2_weight': 0.9,  # 晚间峰值权重
                'peak2_std': 120,     # 晚间峰标准差2小时
                'base_weight': 0.1,   # 基础权重
                'valley_weight': 0.05 # 低谷期权重
            },
            'weekend': {
                'type': 'triple_peak',  # 周末三峰模式：上午+下午+晚间
                'peak1_time': 600,    # 10:00 (上午峰)
                'peak1_weight': 0.4,  # 上午峰值权重
                'peak1_std': 180,     # 上午峰标准差3小时
                'peak2_time': 840,    # 14:00 (下午峰)
                'peak2_weight': 0.5,  # 下午峰值权重
                'peak2_std': 120,     # 下午峰标准差2小时
                'peak3_time': 1200,   # 20:00 (晚间峰)
                'peak3_weight': 0.7,  # 晚间峰值权重
                'peak3_std': 150,     # 晚间峰标准差2.5小时
                'base_weight': 0.2,   # 基础权重(比工作日更高)
                'valley_weight': 0.15 # 低谷期权重(比工作日更高)
            }
        },
        'taxi': {
            'weekday': {
                'type': 'double_peak',
                'peak1_time': 510,    # 8:30 (早高峰后充电)
                'peak2_time': 1260,   # 21:00 (晚高峰后充电)
                'std_dev': 100,       # 标准差约1.67小时
                'base_weight': 0.05,  # 基础权重
                'peak_weight': 1.0,   # 峰值权重
                'peak1_ratio': 1.25,  # 早峰相对晚峰的强度比例
                'valley_weight': 0.05 # 低谷期权重
            },
            'weekend': {
                'type': 'single_peak',  # 周末单峰模式，运营强度降低
                'peak_time': 900,     # 15:00 (下午充电)
                'peak_weight': 0.8,   # 峰值权重(比工作日降低)
                'std_dev': 180,       # 标准差3小时(比工作日更分散)
                'base_weight': 0.15,  # 基础权重(比工作日提高)
                'valley_weight': 0.1  # 低谷期权重
            }
        }
    },
    'station_selection': {
        'max_nearby_stations': 10,
        'wait_time_per_vehicle': 30,  # 分钟
        'time_preference_factor_range': (1.0, 2.5)  # 时间偏好影响范围
    }
}

TWeights = Tuple[float, float, float]  # (行驶时间, 等待时间, 价格)


class ChargingTimePreference:
    """充电时间偏好管理类"""

    def __init__(self, vehicle_type: str = "private_car", is_weekday: bool = True):
        """
        初始化充电时间偏好
        Args:
            vehicle_type: 车辆类型 ("private_car", "taxi")
            is_weekday: 是否为工作日 (True=工作日, False=周末)
        """
        self.vehicle_type = vehicle_type
        self.is_weekday = is_weekday
        self.preference_params = self._get_preference_params(vehicle_type, is_weekday)

    @staticmethod
    def _calculate_gaussian_weight(current_time: int, peak_time: int, std_dev: int) -> float:
        """计算高斯权重的通用方法"""
        return math.exp(-0.5 * ((current_time - peak_time) / std_dev) ** 2)

    def _get_preference_params(self, vehicle_type: str, is_weekday: bool) -> Dict[str, Any]:
        """根据车辆类型和工作日/周末获取偏好参数"""
        if vehicle_type not in ["private_car", "taxi"]:
            raise ValueError(f"不支持的车辆类型: {vehicle_type}，仅支持 'private_car' 和 'taxi'")

        day_type = "weekday" if is_weekday else "weekend"
        return CHARGING_CONFIG['time_preference'][vehicle_type][day_type]
    
    def get_preference_weight(self, current_time_minutes: int) -> float:
        """
        根据当前时间计算充电时间偏好权重
        Args:
            current_time_minutes: 当前时间（分钟，从0点开始计算）
        Returns:
            充电时间偏好权重（0-1之间）
        """
        pref = self.preference_params

        if pref['type'] == 'triple_peak':
            # 三峰正态分布（周末私家车模式）
            return self._calculate_triple_peak_weight(current_time_minutes, pref)
        elif pref['type'] == 'single_peak':
            # 单峰正态分布
            peak_time = pref['peak_time']
            std_dev = pref['std_dev']
            base_weight = pref['base_weight']
            peak_weight = pref['peak_weight']

            # 计算正态分布权重
            gaussian_weight = self._calculate_gaussian_weight(current_time_minutes, peak_time, std_dev)
            return base_weight + (peak_weight - base_weight) * gaussian_weight

        elif pref['type'] == 'double_peak':
            # 双峰正态分布（出租车模式）
            peak1_time = pref['peak1_time']
            peak2_time = pref['peak2_time']
            std_dev = pref['std_dev']
            base_weight = pref['base_weight']
            peak_weight = pref['peak_weight']
            peak1_ratio = pref.get('peak1_ratio', 1.0)  # 早峰相对强度
            valley_weight = pref.get('valley_weight', base_weight)

            # 计算两个峰的权重
            gaussian1 = self._calculate_gaussian_weight(current_time_minutes, peak1_time, std_dev)
            gaussian2 = self._calculate_gaussian_weight(current_time_minutes, peak2_time, std_dev)

            # 早峰权重调整
            weighted_gaussian1 = gaussian1 * peak1_ratio

            # 取两个峰的最大值，但在低谷期使用valley_weight
            max_gaussian = max(weighted_gaussian1, gaussian2)

            # 在两峰之间的低谷期使用更低的权重
            valley_center = (peak1_time + peak2_time) / 2
            valley_distance = abs(current_time_minutes - valley_center)
            valley_range = abs(peak2_time - peak1_time) / 4

            if valley_distance < valley_range and max_gaussian < 0.3:
                return valley_weight

            return base_weight + (peak_weight - base_weight) * max_gaussian

        elif pref['type'] == 'asymmetric_double_peak':
            # 非对称双峰分布（私家车模式）
            peak1_time = pref['peak1_time']
            peak1_weight = pref['peak1_weight']
            peak1_std = pref['peak1_std']
            peak2_time = pref['peak2_time']
            peak2_weight = pref['peak2_weight']
            peak2_std = pref['peak2_std']
            base_weight = pref['base_weight']
            valley_weight = pref.get('valley_weight', base_weight)

            # 分别计算两个峰的权重
            gaussian1 = self._calculate_gaussian_weight(current_time_minutes, peak1_time, peak1_std)
            gaussian2 = self._calculate_gaussian_weight(current_time_minutes, peak2_time, peak2_std)

            # 计算加权权重
            weight1 = base_weight + (peak1_weight - base_weight) * gaussian1
            weight2 = base_weight + (peak2_weight - base_weight) * gaussian2

            # 在白天低谷期（6:00-18:00）使用valley_weight
            if 360 <= current_time_minutes <= 1080:  # 6:00-18:00
                if max(gaussian1, gaussian2) < 0.1:
                    return valley_weight

            return max(weight1, weight2)

        else:
            # 均匀分布
            return pref['base_weight']
    
    def set_preference_params(self, params: Dict[str, Any]):
        """设置偏好参数"""
        self.preference_params = params

    def _calculate_triple_peak_weight(self, current_time_minutes: int, pref: Dict[str, Any]) -> float:
        """
        计算三峰分布权重（周末私家车模式）
        Args:
            current_time_minutes: 当前时间（分钟）
            pref: 偏好参数字典
        Returns:
            充电时间偏好权重
        """
        peak1_time = pref['peak1_time']
        peak1_weight = pref['peak1_weight']
        peak1_std = pref['peak1_std']

        peak2_time = pref['peak2_time']
        peak2_weight = pref['peak2_weight']
        peak2_std = pref['peak2_std']

        peak3_time = pref['peak3_time']
        peak3_weight = pref['peak3_weight']
        peak3_std = pref['peak3_std']

        base_weight = pref['base_weight']
        valley_weight = pref.get('valley_weight', base_weight)

        # 计算三个峰的高斯权重
        gaussian1 = self._calculate_gaussian_weight(current_time_minutes, peak1_time, peak1_std)
        gaussian2 = self._calculate_gaussian_weight(current_time_minutes, peak2_time, peak2_std)
        gaussian3 = self._calculate_gaussian_weight(current_time_minutes, peak3_time, peak3_std)

        # 计算每个峰的加权权重
        weight1 = base_weight + (peak1_weight - base_weight) * gaussian1
        weight2 = base_weight + (peak2_weight - base_weight) * gaussian2
        weight3 = base_weight + (peak3_weight - base_weight) * gaussian3

        # 在深夜低谷期（0:00-6:00, 22:00-24:00）使用valley_weight
        if current_time_minutes <= 360 or current_time_minutes >= 1320:  # 0:00-6:00 or 22:00-24:00
            if max(gaussian1, gaussian2, gaussian3) < 0.1:
                return valley_weight

        # 返回三个峰中的最大权重
        return max(weight1, weight2, weight3)


class ChargingWillingnessEvaluator:
    """充电意愿评估类"""
    
    def __init__(self):
        """初始化充电意愿评估器"""
        pass
    
    def evaluate_slow_charge_willingness(self, ev: 'EV', time: int, cost: float, is_weekday: bool = True) -> bool:
        """
        评估车辆慢充意愿
        Args:
            ev: 电动汽车实例
            time: 当前时间
            cost: 当前慢充费用，$/kWh
            is_weekday: 是否为工作日
        Returns:
            是否愿意慢充
        """
        # 基础充电意愿判断
        basic_willing = cost <= ev._max_sc_cost and time in ev._sc_time

        if not basic_willing:
            return False

        # 考虑充电时间偏好
        current_time_minutes = (time // 60) % 1440  # 转换为一天内的分钟数

        # 根据车辆类型确定时间偏好
        vehicle_type = self._determine_vehicle_type(ev)
        time_preference = ChargingTimePreference(vehicle_type, is_weekday)
        time_preference_weight = time_preference.get_preference_weight(current_time_minutes)

        # 使用随机数和时间偏好权重来决定是否充电
        # 时间偏好权重越高，越容易充电
        return random.random() < time_preference_weight
    
    def evaluate_v2g_willingness(self, ev: 'EV', time: int, earn: float) -> bool:
        """
        评估车辆V2G意愿
        Args:
            ev: 电动汽车实例
            time: 当前时间
            earn: 当前V2G收益，$/kWh
        Returns:
            是否愿意V2G
        """
        return (ev.SOC > ev._kv2g and
                earn >= ev._min_v2g_earn and
                (time in ev._v2g_time if ev._v2g_time else True))
    
    def _determine_vehicle_type(self, ev: 'EV') -> str:
        """确定车辆类型"""
        if ev.is_taxi:
            return "taxi"
        else:
            return "private_car"


class ChargingStationSelector:
    """充电站选择类"""
    
    def __init__(self, fcs_list: 'CSList[FCS]', route_finder: Callable):
        """
        初始化充电站选择器
        Args:
            fcs_list: 快充站列表
            route_finder: 路径查找函数
        """
        self.fcs_list = fcs_list
        self.route_finder = route_finder
    
    def select_best_station(self, ev: 'EV', omega: float, current_edge: Optional[str] = None,
                          current_time: int = 0, is_weekday: bool = True) -> Tuple[list[str], TWeights]:
        """
        选择最佳充电站
        Args:
            ev: 电动汽车实例
            omega: 权重参数
            current_edge: 当前道路
            current_time: 当前时间
            is_weekday: 是否为工作日
        Returns:
            (路径列表, 权重元组) 如果没找到充电站返回 ([], (-1,-1,-1))
        """
        to_charge = ev.charge_target - ev.elec
        
        # 距离检查
        cs_names: list[str] = []
        veh_cnt: list[int] = []
        slots: list[int] = []
        prices: list[float] = []
        stages: list['Stage'] = []
        
        max_stations = CHARGING_CONFIG['station_selection']['max_nearby_stations']
        for cs_i in self.fcs_list.select_near(max_stations):
            cs = self.fcs_list[cs_i]
            if not cs.is_online(current_time):
                continue
            stage = self.route_finder(current_edge, cs.name)
            if ev.is_batt_enough(stage.length):
                cs_names.append(cs.name)
                veh_cnt.append(cs.veh_count())
                slots.append(cs.slots)
                prices.append(cs.pbuy(current_time))
                stages.append(stage)
        
        if len(cs_names) == 0:
            return [], (-1, -1, -1)
        
        return self._calculate_best_station(ev, omega, stages, veh_cnt, slots, prices, current_time, is_weekday)
    
    def _calculate_best_station(self, ev: 'EV', omega: float, stages: list['Stage'],
                              veh_cnt: list[int], slots: list[int], prices: list[float],
                              current_time: int, is_weekday: bool = True) -> Tuple[list[str], TWeights]:
        """计算最佳充电站"""
        t_drive = np.array([t.travelTime for t in stages]) / 60  # 转换为分钟
        wait_time_per_veh = CHARGING_CONFIG['station_selection']['wait_time_per_vehicle']
        t_wait = np.array([max(t - lim, 0) for t, lim in zip(veh_cnt, slots)]) * wait_time_per_veh

        # 计算充电时间偏好权重
        current_time_minutes = (current_time // 60) % 1440
        vehicle_type = self._determine_vehicle_type(ev)
        time_preference = ChargingTimePreference(vehicle_type, is_weekday)
        time_preference_weight = time_preference.get_preference_weight(current_time_minutes)

        # 时间偏好因子：偏好越高，权重越低（更容易被选择）
        time_preference_factor = 2.0 - time_preference_weight  # 范围：1.0-2.0

        # 计算充电目标电量
        to_charge = ev.charge_target - ev.elec

        # 总权重计算
        weight = np.sum([
            omega * (t_drive + t_wait) * time_preference_factor,  # 时间权重
            to_charge * np.array(prices),  # 电价权重
        ], axis=0).tolist()

        wret = tuple(map(lambda x: float(np.mean(x)), (t_drive, t_wait, prices)))
        # 返回权重最小的充电站的路径和权重
        best_idx = np.argmin(weight)
        return stages[best_idx].edges, wret
    
    def get_nearest_station(self, current_edge: str, current_time: int) -> Tuple[Optional[str], float, Optional['Stage']]:
        """
        获取最近的充电站
        Args:
            current_edge: 当前道路
            current_time: 当前时间
        Returns:
            (充电站名称, 距离, 路径阶段)
        """
        min_cs_name = None
        min_cs_dist = 1e400
        min_cs_stage = None
        
        for cs in self.fcs_list.get_online_CS_names(current_time):
            route = self.route_finder(current_edge, cs)
            if route.length < min_cs_dist:
                min_cs_dist = route.length
                min_cs_name = cs
                min_cs_stage = route
        
        return min_cs_name, min_cs_dist, min_cs_stage
    
    def _determine_vehicle_type(self, ev: 'EV') -> str:
        """确定车辆类型"""
        if ev.is_taxi:
            return "taxi"
        else:
            return "private_car"


class ChargingDecisionManager:
    """充电决策管理器 - 统一的充电决策接口"""

    def __init__(self, fcs_list: 'CSList[FCS]', route_finder: Callable, config: Optional[Dict] = None):
        """
        初始化充电决策管理器
        Args:
            fcs_list: 快充站列表
            route_finder: 路径查找函数
            config: 配置参数字典
        """
        self.config = config or CHARGING_CONFIG
        self.willingness_evaluator = ChargingWillingnessEvaluator()
        self.station_selector = ChargingStationSelector(fcs_list, route_finder)

    def should_fast_charge(self, ev: 'EV', trip: 'Trip', enable_distance_based: bool = True) -> bool:
        """
        判断是否需要快充
        Args:
            ev: 电动汽车实例
            trip: 行程信息
            enable_distance_based: 是否启用基于距离的充电决策
        Returns:
            是否需要快充
        """
        if enable_distance_based:
            # 基于距离的充电决策需要在调用处计算路径长度
            # 这里只提供SOC阈值判断作为备选
            return ev.SOC < ev._kfc
        else:
            # 基于SOC阈值的充电决策
            return ev.SOC < ev._kfc

    def select_charging_station(self, ev: 'EV', omega: float, current_edge: Optional[str] = None,
                              current_time: int = 0, is_weekday: bool = True) -> Tuple[list[str], TWeights]:
        """
        选择充电站
        Args:
            ev: 电动汽车实例
            omega: 权重参数
            current_edge: 当前道路
            current_time: 当前时间
            is_weekday: 是否为工作日
        Returns:
            (路径列表, 权重元组)
        """
        return self.station_selector.select_best_station(ev, omega, current_edge, current_time, is_weekday)

    def should_slow_charge(self, ev: 'EV', time: int, cost: float, is_weekday: bool = True) -> bool:
        """
        判断是否应该慢充
        Args:
            ev: 电动汽车实例
            time: 当前时间
            cost: 充电费用
            is_weekday: 是否为工作日
        Returns:
            是否应该慢充
        """
        return self.willingness_evaluator.evaluate_slow_charge_willingness(ev, time, cost, is_weekday)

    def should_v2g(self, ev: 'EV', time: int, earn: float) -> bool:
        """
        判断是否应该V2G
        Args:
            ev: 电动汽车实例
            time: 当前时间
            earn: V2G收益
        Returns:
            是否应该V2G
        """
        return self.willingness_evaluator.evaluate_v2g_willingness(ev, time, earn)

    def calculate_charge_target(self, ev: 'EV', destination_edge: str, enable_distance_based: bool = True) -> float:
        """
        计算充电目标
        Args:
            ev: 电动汽车实例
            destination_edge: 目的地道路
            enable_distance_based: 是否启用基于距离的充电量计算
        Returns:
            充电目标电量 (kWh)
        """
        if enable_distance_based:
            # 基于距离的充电量计算需要在调用处提供路径信息
            # 这里返回满电作为默认值
            return ev.full_battery
        else:
            # 默认充满电
            return ev.full_battery

    def get_nearest_charging_station(self, current_edge: str, current_time: int) -> Tuple[Optional[str], float, Optional['Stage']]:
        """
        获取最近的充电站
        Args:
            current_edge: 当前道路
            current_time: 当前时间
        Returns:
            (充电站名称, 距离, 路径阶段)
        """
        return self.station_selector.get_nearest_station(current_edge, current_time)
