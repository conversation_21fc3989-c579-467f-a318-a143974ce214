from collections import defaultdict
from itertools import chain
from feasytools import TimeImplictFunc
from fpowerkit import Grid, FloatVar, GridSolveResult, CombinedSolver, Estimator, Calculator

from ..traffic import DetectFiles, CS
from .base import *

def _sv(x)->float:
    assert x is not None, "求解失败"
    return x

def _isfloat(x):
    try:
        float(x)
        return True
    except:
        return False

class PluginPDN(PluginBase[float], IGridPlugin):
    @property
    def Description(self)->str:
        return "配电网模型"
    
    @staticmethod
    def __create_closure(cslist: list[CS], Sb_MVA: float):
        def _sumP():
            return sum(c.Pc_MW for c in cslist)/Sb_MVA
        return _sumP
    


    @staticmethod
    def ElemShouldHave() -> ConfigDict:
        '''Get the plugin configuration item list'''
        EST = f"'{Estimator.DistFlow.value}'"
        CAL = f"'{Calculator.OpenDSS.value}','{Calculator.Newton.value}','{Calculator.NoneSolver.value}'"
        return ConfigDict([
            PluginConfigItem("estimator", "combo={'values': ["+EST+"]}", f"Estimator of the solver, must be {EST}", Estimator.DistFlow.value),
            PluginConfigItem("calculator", "combo={'values': ["+CAL+"]}", f"Calculator of the solver, can be {CAL}", Calculator.NoneSolver.value),
            PluginConfigItem("MLRP", "entry", "Maximum load reduction percentage", 0.5),
            PluginConfigItem("source_bus", "entry", "Source bus of the grid", ""),
            PluginConfigItem("DecBuses", "entry", "List of buses for load reduction, separated by commas", ""),
            PluginConfigItem("SmartCharge", "combo={'values': ['YES', 'NO']}", "Whether to enable smart charging, 'YES' or 'NO'", "NO"),
        ])
    
    def Init(self, elem:ET.Element, inst:TrafficInst, work_dir:Path,
            res_dir:Path, plg_deps:'list[PluginBase]') -> float:
        '''Initialize the plugin from the XML element'''
        self.__inst = inst
        self.SetPreStep(self.PreStep)
        self.__fh = open(res_dir / "pdn_res.log", "w", encoding='utf-8')
        self.SetPostSimulation(self.__close_and_summary)
        print(f"[PDN DEBUG] PDN插件初始化完成，日志文件: {res_dir / 'pdn_res.log'}", file=self.__fh)
        self.__fh.flush()

        res = DetectFiles(str(work_dir))
        assert res.grid, "未指定电网文件"
        self.__gr = Grid.fromFile(res.grid, True)
        decs = list(map(lambda x:x.strip(), elem.get("DecBuses","").split(",")))
        if elem.get("SmartCharge", "NO") == "NO":
            decs.clear()
        self.__sol = CombinedSolver(self.__gr,
            estimator=Estimator(elem.get("estimator","DistFlow")),
            calculator=Calculator(elem.get("calculator", "None")),
            mlrp=float(elem.get("MLRP","0.5")),
            source_bus=elem.get("source_bus", ""),
        )
        self.__sol.SetErrorSaveTo(str(res_dir / "pdn_logs"))
        self.__badcnt = 0
        self.__pds:dict[str, list[CS]] = defaultdict(list)
        for c in chain(inst.FCSList,inst.SCSList):
            if not c.node in self.__gr.BusNames:
                raise ValueError(f"错误: 充电站{c.name}的母线{c.node}在电网中不存在")
            self.__pds[c.node].append(c)
        for b, css in self.__pds.items():
            v = TimeImplictFunc(self.__create_closure(css, self.__gr.Sb_MVA))
            self.__gr.Bus(b).Pd += v
            if b in decs:
                self.__sol.est.AddReduce(b, v)
        self.last_ok = GridSolveResult.Failed
        return 1e100

    def __close_and_summary(self):
        """关闭文件并输出总结"""
        print(f"\n[PDN 总结] 仿真完成，总共求解失败 {self.__badcnt} 次", file=self.__fh)
        print(f"[PDN 总结] 求解成功率: {((24-self.__badcnt)/24*100):.1f}%", file=self.__fh)
        self.__fh.flush()
        self.__fh.close()
        print(f"[PDN 总结] 仿真完成，总共求解失败 {self.__badcnt} 次，成功率: {((24-self.__badcnt)/24*100):.1f}%")

    def isSmartChargeEnabled(self)->bool:
        '''Check if smart charging is enabled'''
        return len(self.__sol.est.DecBuses) > 0
    
    @property
    def Solver(self):
        return self.__sol
    
    @property
    def Grid(self)->Grid:
        '''Get the grid instance'''
        return self.__gr
    
    def PreStep(self, _t:int, /, sta:PluginStatus)->tuple[bool,float]:
        '''Solve the optimal generation plan of the distribution network at time _t, 
        the solution result can be found in the relevant values of the Grid instance'''
        if sta == PluginStatus.EXECUTE:
            ok, val = self.__sol.solve(_t)
            if ok == GridSolveResult.Failed:
                self.__badcnt += 1
                # 详细记录失败信息
                print(f"[PDN DEBUG] 时间 {_t}s 求解失败 (第{self.__badcnt}次)", file=self.__fh)
                print(f"[PDN DEBUG] 当前总负荷: {sum(sum(c.Pc for c in css) for css in self.__pds.values()):.3f} MW", file=self.__fh)
                print(f"[PDN DEBUG] 各母线负荷分布:", file=self.__fh)
                for bus_name, css in self.__pds.items():
                    total_load = sum(c.Pc for c in css)
                    if total_load > 0:
                        print(f"  {bus_name}: {total_load:.3f} MW ({len(css)}个充电站)", file=self.__fh)
                self.__fh.flush()

                if self.__badcnt>0 and self.__badcnt % 20 == 0:
                    print(f"[PDN]总共求解失败{self.__badcnt}次")
                if self.last_ok:
                    print(f"[PDN]在时间{_t}未能求解配电网. 总共失败{self.__badcnt}次")
            else:
                self.__gr.ApplyAllESS(_t-self.LastTime)
                if ok == GridSolveResult.OKwithoutVICons or ok == GridSolveResult.SubOKwithoutVICons:
                    print(f"t={_t}, Relax!", file = self.__fh)
                if self.isSmartChargeEnabled():
                    for c in chain(self.__inst.FCSList,self.__inst.SCSList):
                        c.set_Pc_lim(float("inf"))
                    for b,x in self.__sol.est.DecBuses.items():
                        if x.Reduction:
                            tot = x.Limit(_t)
                            k = (tot - x.Reduction) / tot
                            print(f"t={_t}, Load reduction at bus {b}:",
                                  f"{x.Reduction*self.__gr.Sb_kVA:.2f} kW", file = self.__fh)
                            for c in self.__pds[b]:
                                l = k * c.Pc
                                c.set_Pc_lim(l)
                                print(f"    CS {c.name}: {l*3600:.2f} kW <- {c.Pc_kW:.2f} kW", file = self.__fh)
            if ok != GridSolveResult.Failed and self.last_ok == GridSolveResult.Failed and self.__badcnt>0:
                print(f"[PDN]从时间{_t}开始成功求解配电网. 之前总共失败{self.__badcnt}次")
            self.last_ok = ok
            return ok != GridSolveResult.Failed, val
        elif sta == PluginStatus.OFFLINE:
            return True, 0
        elif sta == PluginStatus.HOLD:
            return self.LastPostStepSucceed, self.LastPreStepResult
    
    @property
    def BestCost(self)->float: 
        '''Get the cost of the best generation plan, requires the last solve to be successful'''
        assert self.LastPreStepSucceed, "上次求解失败"
        return self.LastPreStepResult

    @property
    def GeneratorPlan(self)->dict[str,float]:
        '''Get the best generation plan, requires the last solve to be successful'''
        assert self.LastPreStepSucceed, "上次求解失败"
        return {g.ID: _sv(g.P) for g in self.__gr.Gens}