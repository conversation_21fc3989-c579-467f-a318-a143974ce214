from dataclasses import dataclass
from pathlib import Path
import random, string, gzip
from typing import Optional
from xml.etree import ElementTree as ET

from typing import List, Tuple, Union

# 类型定义
IntPairList = List[Tuple[int, int]]      # 整数对列表
PriceList = Tuple[List[int], List[float]]  # 价格列表(时间, 价格)
TWeights = Tuple[float, float, float]     # 三元权重元组

_letters = string.ascii_letters + string.digits


def random_string(length: int):
    """生成指定长度的随机字符串"""
    return "".join(random.choice(_letters) for _ in range(length))

def ReadXML(file: str, compressed: Optional[bool] = None) -> ET.ElementTree:
    '''
    读取XML文件，支持压缩的GZ文件
        file: 文件路径
        compressed: 文件是否压缩。如果为None，函数会自动检测，但只支持.xml和.xml.gz格式
    '''
    filel = file.lower()
    if filel.endswith(".xml.gz") or compressed == True:
        with gzip.open(file, "rt", encoding="utf8") as f:
            return ET.ElementTree(file=f)
    elif filel.endswith(".xml") or compressed == False:
        return ET.ElementTree(file=file)
    else:
        raise RuntimeError(f"错误: 不支持的XML文件{file}类型")


    
def GetTimeAndNetwork(file: str):
    """
    解析SUMO配置文件获取仿真时间和网络文件
    Returns:
        bt (int): 开始时间
        et (int): 结束时间
        nf (str): 网络文件路径
    """
    root = ReadXML(file,compressed=False).getroot()
    if root is None:
        raise RuntimeError(f"错误: 不支持的XML文件{file}类型")
    bt, et = -1, -1
    tnode = root.find("time")
    if isinstance(tnode, ET.Element):
        bnode = tnode.find("begin")
        enode = tnode.find("end")
        if isinstance(bnode, ET.Element) and isinstance(enode, ET.Element):
            bt, et = int(bnode.attrib.get("value", "-1")), int(enode.attrib.get("value", "-1")),
    
    nf = None
    inode = root.find("input")
    if isinstance(inode, ET.Element):
        nfnode = inode.find("net-file")
        if isinstance(nfnode, ET.Element):
            nf = nfnode.attrib.get("value")
    
    assert nf != None, "必须定义网络文件!"
    return bt, et, nf



@dataclass
class FileDetectResult:
    name: str
    fcs: Optional[str] = None
    scs: Optional[str] = None
    grid: Optional[str] = None
    net: Optional[str] = None
    plg: Optional[str] = None
    cfg: Optional[str] = None
    taz: Optional[str] = None
    py: Optional[str] = None
    taz_type: Optional[str] = None
    osm: Optional[str] = None
    poly: Optional[str] = None
    cscsv: Optional[str] = None
    weekday_veh: Optional[str] = None  # 工作日车辆文件
    weekend_veh: Optional[str] = None  # 周末车辆文件
    
    def __getitem__(self, key: str):
        return getattr(self, key)
    
    def has(self, key: str) -> bool:
        return hasattr(self, key)
    
    def get(self, key: str) -> Optional[str]:
        return getattr(self, key, None)
    
    def __contains__(self, key: str) -> bool:
        return hasattr(self, key) and getattr(self, key) != None

def ReadSUMONet(file: str):
    """
    读取SUMO网络文件并返回sumolib.net.Net对象
    """
    import sumolib
    ret = sumolib.net.readNet(file)
    assert isinstance(ret, sumolib.net.Net), "读取SUMO网络文件失败"
    return ret

def DetectFiles(dir: str) -> FileDetectResult:
    """
    检测指定目录中的仿真相关文件(SUMO配置、慢充站、快充站、电网等)
    """
    p = Path(dir)
    ret: dict[str, str] = {"name": p.name}
    weekday_veh = None  # 工作日车辆文件
    weekend_veh = None  # 周末车辆文件

    # 文件类型模式映射
    FILE_PATTERNS = {
        "fcs": [".fcs.xml", ".fcs.xml.gz"],
        "scs": [".scs.xml", ".scs.xml.gz"],
        "grid": [".grid.zip", ".grid.xml"],
        "net": [".net.xml", ".net.xml.gz"],
        "veh": [".veh.xml", ".veh.xml.gz"],
        "plg": [".plg.xml", ".plg.xml.gz"],
        "cfg": [".sumocfg"],
        "taz": [".taz.xml", ".taz.xml.gz"],
        "py": [".py"],
        "taz_type": ["taz_type.txt"],
        "osm": [".osm.xml", ".osm.xml.gz"],
        "poly": [".poly.xml", ".poly.xml.gz"],
        "cscsv": ["cs.csv"],
    }

    def _detect_file_type(filename: str) -> Optional[str]:
        """检测文件类型"""
        filename_lower = filename.lower()
        for file_type, patterns in FILE_PATTERNS.items():
            if any(filename_lower.endswith(pattern) for pattern in patterns):
                return file_type
        return None

    def add(name: str, filename: str):
        if name == "veh":
            # 根据文件名判断是工作日还是周末车辆文件
            filename_lower = filename.lower()
            if ".weekday." in filename_lower:
                if weekday_veh is not None:
                    raise FileExistsError(f"错误: 发现多个工作日车辆文件: {weekday_veh} 和 {filename}")
                nonlocal weekday_veh
                weekday_veh = filename
            elif ".weekend." in filename_lower:
                if weekend_veh is not None:
                    raise FileExistsError(f"错误: 发现多个周末车辆文件: {weekend_veh} 和 {filename}")
                nonlocal weekend_veh
                weekend_veh = filename
            else:
                raise ValueError(f"错误: 车辆文件必须包含 .weekday. 或 .weekend. 标识: {filename}")
        else:
            if name in ret:
                raise FileExistsError(f"错误: 配置文件夹中{name}有重复: {ret[name]}和{filename}")
            ret[name] = filename

    # 遍历目录中的文件
    for x in p.iterdir():
        if not x.is_file():
            continue
        filename = str(x)
        file_type = _detect_file_type(filename)
        if file_type:
            add(file_type, filename)

    # 保存车辆文件信息
    ret["weekday_veh"] = weekday_veh
    ret["weekend_veh"] = weekend_veh

    return FileDetectResult(**ret)

