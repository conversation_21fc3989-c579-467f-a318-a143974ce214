from abc import abstractmethod, ABC
from collections import deque
from dataclasses import dataclass
from itertools import chain
from typing import Callable, Optional, Sequence
from feasytools import RangeList, makeFunc, OverrideFunc, TimeFunc, ConstFunc, SegFunc
from ordered_set import OrderedSet
from .evdict import EVDict
from .utils import IntPairList, PriceList


class ChargingStationError(Exception):
    """充电站相关异常"""
    pass


def _get_price_xml(time_func: TimeFunc, element_name: str) -> str:
    """将时间函数转换为XML格式"""
    if isinstance(time_func, ConstFunc):
        return f'<{element_name}>\n  <item btime="0" price="{time_func._val}" />\n</{element_name}>'
    elif isinstance(time_func, SegFunc):
        return time_func.toXML(element_name, "item", "btime", "price")
    elif isinstance(time_func, OverrideFunc):
        return _get_price_xml(time_func._val, element_name)
    else:
        raise ValueError(f"不支持的TimeFunc类型: {type(time_func)}")

@dataclass
class AllocationEnvironment:
    """充电分配环境数据类"""
    charging_station: 'CS'
    vehicle_ids: Sequence[str]
    vehicle_dict: EVDict
    current_time: int

V2GAllocator = Callable[[AllocationEnvironment, float], list[float]]

def _average_v2g_allocator(env: AllocationEnvironment, v2g_coefficient: float) -> list[float]:
    """平均V2G功率分配"""
    return len(env.vehicle_ids) * [v2g_coefficient]

class V2GAllocPool:
    """V2G功率分配算法池"""
    _pool: 'dict[str, V2GAllocator]' = {"Average": _average_v2g_allocator}

    @staticmethod
    def add(name: str, func: V2GAllocator):
        V2GAllocPool._pool[name] = func

    @staticmethod
    def get(name: str) -> V2GAllocator:
        return V2GAllocPool._pool[name]

MaxPowerAllocator = Callable[[AllocationEnvironment, int, float, float], list[float]]

def _average_max_power_allocator(env: AllocationEnvironment, vehicle_count: int, max_power_per_slot: float, max_total_power: float) -> list[float]:
    """平均分配充电功率"""
    _ = env  # 未使用参数
    power_per_vehicle = min(max_total_power / vehicle_count, max_power_per_slot) if vehicle_count > 0 else 0
    return [power_per_vehicle] * vehicle_count

def _prioritized_max_power_allocator(env: AllocationEnvironment, vehicle_count: int, max_power_per_slot: float, max_total_power: float) -> list[float]:
    """优先级分配充电功率"""
    _ = env  # 未使用参数
    power_allocations = []
    remaining_power = max_total_power

    for _ in range(vehicle_count):
        if remaining_power > max_power_per_slot:
            power_allocations.append(max_power_per_slot)
            remaining_power -= max_power_per_slot
        else:
            power_allocations.append(remaining_power)
            remaining_power = 0
    return power_allocations

def _time_based_max_power_allocator(env: AllocationEnvironment, vehicle_count: int, max_power_per_slot: float, max_total_power: float) -> list[float]:
    """基于时间的充电功率分配"""
    _ = vehicle_count  # 未使用参数
    vehicle_priorities = []
    for i, vehicle_id in enumerate(env.vehicle_ids):
        vehicle = env.vehicle_dict[vehicle_id]
        remaining_time = max(0, vehicle.trip.depart_time - env.current_time)
        vehicle_priorities.append((remaining_time, i))

    vehicle_priorities.sort()
    power_allocations = []
    remaining_power = max_total_power

    for _, _ in vehicle_priorities:
        if remaining_power > max_power_per_slot:
            power_allocations.append(max_power_per_slot)
            remaining_power -= max_power_per_slot
        else:
            power_allocations.append(remaining_power)
            remaining_power = 0
    return power_allocations

class MaxPowerAllocPool:
    """
    最大充电功率分配算法池

    管理和提供各种最大充电功率分配算法，支持动态添加和获取算法。
    提供三种默认算法：平均分配、优先级分配、基于时间分配。
    """
    _pool: 'dict[str, MaxPowerAllocator]' = {
        "Average": _average_max_power_allocator,      # 平均分配算法
        "Prioritized": _prioritized_max_power_allocator,  # 优先级分配算法
        "TimeBased": _time_based_max_power_allocator,  # 基于时间的分配算法
    }

    @staticmethod
    def add(name: str, func: MaxPowerAllocator):
        """
        添加最大充电功率分配算法

        Args:
            name: 算法名称
            func: 算法函数
        """
        MaxPowerAllocPool._pool[name] = func

    @staticmethod
    def get(name: str) -> MaxPowerAllocator:
        """
        获取最大充电功率分配算法
        """
        return MaxPowerAllocPool._pool[name]
    

class CS(ABC):
    """充电站抽象基类"""

    @abstractmethod
    def __init__(self, name: str, slots: int, bus: str, x: float, y: float, offline: IntPairList,
                 max_pc: float, max_pd: float, price_buy: PriceList, price_sell: PriceList,
                 pc_alloc: str = "Average", pd_alloc: str = "Average"):
        self._name = name
        self._slots = slots
        self._node = bus
        self._offline = RangeList(offline)
        self._manual_offline: Optional[bool] = None
        self._pbuy = OverrideFunc(makeFunc(*price_buy))
        self._psell = None if price_sell == () else OverrideFunc(makeFunc(*price_sell))
        self._max_power_per_slot = max_pc
        self._max_total_power = float("inf")
        self._power_alloc_method = pc_alloc
        self._power_allocator = MaxPowerAllocPool.get(pc_alloc)
        self._actual_power_limits: Optional[list[float]] = None
        self._max_v2g_power_per_slot = max_pd
        self._v2g_alloc_method = pd_alloc
        self._v2g_allocator = V2GAllocPool.get(pd_alloc)
        self._actual_v2g_ratios: list[float] = []
        self._current_charging_load = 0.0
        self._current_discharge_load = 0.0
        self._current_v2g_capacity = 0.0
        self._x = x
        self._y = y
    
    def __repr__(self):
        return f"CS(name='{self._name}',slots={self._slots},pbuy={self._pbuy},psell={self._psell},offline={self._offline})"

    def __str__(self):
        return f"CS(name='{self._name}')"

    @abstractmethod
    def to_xml(self) -> str:
        """将充电站配置转换为XML格式"""
        ...

    @property
    def x(self) -> float:
        return self._x

    @property
    def y(self) -> float:
        return self._y

    @property
    def name(self) -> str:
        return self._name

    @property
    def slots(self) -> int:
        return self._slots

    @property
    def node(self) -> str:
        return self._node

    @property
    def pbuy(self) -> OverrideFunc:
        return self._pbuy

    @property
    def psell(self) -> OverrideFunc:
        if self._psell is None:
            raise AttributeError("充电站%s不支持V2G功能" % self.name)
        return self._psell

    @property
    def supports_V2G(self) -> bool:
        return self._psell is not None

    def is_online(self, t: int) -> bool:
        if self._manual_offline is not None:
            return not self._manual_offline
        return not t in self._offline

    def force_shutdown(self):
        self._manual_offline = True

    def force_reopen(self):
        self._manual_offline = False

    def clear_manual_offline(self):
        self._manual_offline = None

    @abstractmethod
    def add_veh(self, veh_id: str) -> bool:
        """
        将车辆添加到充电队列中。当充电桩不足时等待。
        """
        raise NotImplementedError

    @abstractmethod
    def pop_veh(self, veh_id: str) -> bool:
        """
        从充电队列中移除车辆
        """
        raise NotImplementedError

    @abstractmethod
    def __contains__(self, veh_id: str) -> bool:
        """
        检查充电站是否包含指定车辆
        """
        pass

    def has_veh(self, veh_id: str) -> bool:
        """
        检查是否存在指定ID的车辆
        """
        return self.__contains__(veh_id)

    @abstractmethod
    def __len__(self) -> int:
        """返回充电站中车辆的数量"""
        raise NotImplementedError

    @abstractmethod
    def update(
        self, ev_dict: EVDict, sec: int, cur_time: int, v2g_k: float
    ) -> list[str]:
        """
        使用当前参数对给定EVDict中的电动汽车进行sec秒的充放电操作

        Args:
            ev_dict: 与此充电站中存储的车辆ID对应的EVDict
            sec: 时间间隔（秒）
            cur_time: 当前时间
            v2g_k: V2G反向功率比例

        Returns:
            从充电站移除的车辆ID列表
        """
        raise NotImplementedError

    @abstractmethod
    def is_charging(self, veh_id: str) -> bool:
        """
        获取车辆的充电状态。如果车辆不存在，将抛出ValueError。

        Args:
            veh_id: 车辆ID

        Returns:
            True表示正在充电，False表示正在等待
        """
        raise NotImplementedError

    @abstractmethod
    def veh_count(self, only_charging: bool = False) -> int:
        """
        返回充电站中车辆的数量

        Args:
            only_charging: 当为True时，只返回正在充电的车辆数量

        Returns:
            车辆数量
        """
        raise NotImplementedError

    @abstractmethod
    def get_V2G_cap(self, ev_dict: EVDict) -> float:
        """
        根据EVDict中给定的电动汽车进行充放电，获取当前情况下V2G的最大功率

        Args:
            ev_dict: 与此充电站中存储的车辆ID对应的EVDict

        Returns:
            V2G最大功率，单位kWh/s
        """
        raise NotImplementedError
    
    def set_max_power_limit(self, value: float):
        """设置充电站的最大充电功率"""
        self._max_total_power = value

    @property
    def Pc(self) -> float:
        """当前充电功率，kWh/s"""
        return self._current_charging_load

    @property
    def Pc_kW(self) -> float:
        """当前充电功率，kW"""
        return self._current_charging_load * 3600

    @property
    def Pc_MW(self) -> float:
        """当前充电功率，MW"""
        return self._current_charging_load * 3.6

    @property
    def Pd(self) -> float:
        """当前V2G放电功率，kWh/s"""
        return self._current_discharge_load

    @property
    def Pd_kW(self) -> float:
        """当前V2G放电功率，kW"""
        return self._current_discharge_load * 3600

    @property
    def Pd_MW(self) -> float:
        """当前V2G放电功率，MW"""
        return self._current_discharge_load * 3.6

    @property
    def Pv2g(self) -> float:
        """当前最大V2G放电功率，kWh/s"""
        return self._current_v2g_capacity

    @property
    def Pv2g_kW(self) -> float:
        """当前最大V2G放电功率，kW"""
        return self._current_v2g_capacity * 3600

    @property
    def Pv2g_MW(self) -> float:
        """当前最大V2G放电功率，MW"""
        return self._current_v2g_capacity * 3.6
    
    def vehicles(self):
        """获取充电站中所有车辆的迭代器"""
        raise NotImplementedError

    def averageSOC(self, ev_dict: EVDict) -> float:
        """计算充电站中所有车辆的平均SOC"""
        raise NotImplementedError


class SCS(CS):
    """
    慢充站类

    实现慢速充电站的具体功能，支持V2G双向充放电。慢充站通常用于
    长时间停车场景，如住宅区、办公区等，充电功率相对较低但支持V2G功能。

    主要特点：
    - 支持慢速充电和V2G放电
    - 车辆可以长时间停留
    - 充满电后车辆进入空闲状态，可参与V2G
    - 支持多种功率分配策略
    """

    def __init__(self, *args, **kwargs):
        """初始化慢充站"""
        super().__init__(*args, **kwargs)
        self._charging_vehicles: OrderedSet[str] = OrderedSet([])  # 正在充电的车辆
        self._idle_vehicles: set[str] = set()                 # 已充满电的车辆（可参与V2G）

    def to_xml(self) -> str:
        """
        将慢充站配置转换为XML格式

        Returns:
            XML格式的慢充站配置字符串
        """
        ret = f'<scs name="{self._name}" edge="{self._name}" slots="{self._slots}" bus="{self._node}" ' \
            f'x="{self._x}" y="{self._y}" max_pc="{self._max_power_per_slot * 3600:.2f}" max_pd="{self._max_v2g_power_per_slot * 3600:.2f}" ' \
            f'pc_alloc="{self._power_alloc_method}" pd_alloc="{self._v2g_alloc_method}">\n'
        ret += _get_price_xml(self._pbuy, "pbuy") + "\n"
        if self._psell:
            ret += _get_price_xml(self._psell, "psell") + "\n"
        if len(self._offline) > 0:
            ret += self._offline.toXML("offline") + "\n"
        ret += "</scs>"
        return ret
    
    def add_veh(self, veh_id: str) -> bool:
        """
        将车辆添加到慢充站

        Args:
            veh_id: 车辆ID

        Returns:
            True表示添加成功，False表示车辆已存在或充电站已满
        """
        if veh_id in self._charging_vehicles or veh_id in self._idle_vehicles:
            return False
        if len(self._charging_vehicles) + len(self._idle_vehicles) < self._slots:
            self._charging_vehicles.add(veh_id)
            return True
        else:
            return False

    def pop_veh(self, veh_id: str) -> bool:
        """
        从慢充站移除车辆

        Args:
            veh_id: 车辆ID

        Returns:
            True表示移除成功，False表示车辆不存在
        """
        try:
            self._charging_vehicles.remove(veh_id)
            return True
        except KeyError:
            try:
                self._idle_vehicles.remove(veh_id)
                return True
            except KeyError:
                return False

    def __contains__(self, veh_id: str) -> bool:
        """检查慢充站是否包含指定车辆"""
        return veh_id in self._charging_vehicles or veh_id in self._idle_vehicles

    def __len__(self) -> int:
        """返回慢充站中车辆总数"""
        return len(self._charging_vehicles) + len(self._idle_vehicles)

    def is_charging(self, veh_id: str) -> bool:
        """检查指定车辆是否正在充电"""
        return veh_id in self._charging_vehicles

    def veh_count(self, only_charging: bool = False) -> int:
        """
        返回慢充站中车辆数量

        Args:
            only_charging: 是否只统计正在充电的车辆

        Returns:
            车辆数量
        """
        if only_charging:
            return len(self._charging_vehicles)
        else:
            return len(self._charging_vehicles) + len(self._idle_vehicles)

    def get_V2G_cap(self, ev_dict: EVDict, current_time: int) -> float:
        """
        计算慢充站的V2G容量

        Args:
            ev_dict: 车辆字典
            current_time: 当前时间

        Returns:
            V2G总容量，单位kWh/s
        """
        if not self.is_online(current_time):
            return 0.0

        if self._psell is None:
            return 0.0

        total_v2g_capacity = 0.0
        v2g_price = self._psell(current_time)

        for vehicle_id in chain(self._charging_vehicles, self._idle_vehicles):
            vehicle = ev_dict[vehicle_id]
            if vehicle.willing_to_v2g(current_time, v2g_price):
                total_v2g_capacity += vehicle.max_v2g_rate * vehicle.eta_discharge

        self._current_v2g_capacity = total_v2g_capacity
        return total_v2g_capacity

    def vehicles(self):
        """返回慢充站中所有车辆的迭代器（包括充电中和空闲的）"""
        return chain(self._charging_vehicles, self._idle_vehicles)

    def averageSOC(self, ev_dict: EVDict) -> float:
        """
        计算正在充电车辆的平均SOC

        Args:
            ev_dict: 车辆字典

        Returns:
            平均SOC值
        """
        if len(self._charging_vehicles) == 0:
            return 0.0
        return sum([ev_dict[vehicle_id].SOC for vehicle_id in self._charging_vehicles]) / len(self._charging_vehicles)
    
    def _handle_charging(self, ev_dict: EVDict, sec: int, cur_time: int) -> tuple[float, list[str]]:
        """
        处理车辆充电逻辑

        Returns:
            tuple: (总充电功率, 充电完成的车辆列表)
        """
        if len(self._charging_vehicles) == 0:
            return 0.0, []

        total_charge_power = 0.0
        completed_vehicles = []

        # 分配充电功率
        self._actual_power_limits = self._power_allocator(
            AllocationEnvironment(self, self._charging_vehicles, ev_dict, cur_time),
            len(self._charging_vehicles),
            self._max_power_per_slot,
            self._max_total_power
        )

        # 执行充电
        for i, vehicle_id in enumerate(self._charging_vehicles):
            vehicle = ev_dict[vehicle_id]
            is_weekday = (cur_time // 86400) % 7 < 5  # 假设仿真从周一开始

            if vehicle.willing_to_slow_charge(cur_time, self._pbuy(cur_time), is_weekday):
                actual_power = min(self._actual_power_limits[i], vehicle._esc_rate)
                total_charge_power += vehicle.charge(sec, self._pbuy(cur_time), actual_power)

                # 检查是否充电完成
                v2g_factor = min(1, vehicle._kv2g) if self._psell is not None else 1
                if vehicle._elec >= vehicle._chtar * v2g_factor:
                    completed_vehicles.append(vehicle_id)

        return total_charge_power, completed_vehicles

    def _handle_v2g(self, ev_dict: EVDict, sec: int, cur_time: int, v2g_coefficient: float) -> float:
        """
        处理V2G放电逻辑

        Returns:
            总放电功率
        """
        if v2g_coefficient <= 0 or self._psell is None:
            return 0.0

        total_discharge_power = 0.0
        v2g_price = self._psell(cur_time)

        # 找出愿意参与V2G的车辆
        v2g_vehicles = [
            vehicle_id for vehicle_id in self._idle_vehicles
            if ev_dict[vehicle_id].willing_to_v2g(cur_time, v2g_price)
        ]

        if not v2g_vehicles:
            return 0.0

        # 分配V2G功率
        self._actual_v2g_ratios = self._v2g_allocator(
            AllocationEnvironment(self, v2g_vehicles, ev_dict, cur_time),
            v2g_coefficient
        )

        # 执行V2G放电
        for vehicle_id, ratio in zip(v2g_vehicles, self._actual_v2g_ratios):
            total_discharge_power += ev_dict[vehicle_id].discharge(
                v2g_coefficient * ratio, sec, self._psell(cur_time)
            )

        return total_discharge_power

    def update(self, ev_dict: EVDict, sec: int, cur_time: int, v2g_k: float) -> list[str]:
        """
        更新慢充站状态，处理充电和V2G

        Args:
            ev_dict: 车辆字典
            sec: 时间间隔（秒）
            cur_time: 当前时间
            v2g_k: V2G功率系数

        Returns:
            从充电站移除的车辆ID列表（空列表，因为慢充站车辆不会自动离开）
        """
        if not self.is_online(cur_time):
            self._current_charging_load = 0.0
            self._current_discharge_load = 0.0
            return []

        # 处理充电
        total_charge_power, completed_vehicles = self._handle_charging(ev_dict, sec, cur_time)

        # 将充电完成的车辆转移到空闲状态
        for vehicle_id in completed_vehicles:
            self._charging_vehicles.remove(vehicle_id)
            self._idle_vehicles.add(vehicle_id)

        # 处理V2G
        total_discharge_power = self._handle_v2g(ev_dict, sec, cur_time, v2g_k)

        # 更新功率状态
        self._current_charging_load = total_charge_power / sec
        self._current_discharge_load = total_discharge_power / sec

        return []  # 慢充站车辆不会自动离开


class FCS(CS):
    """
    快充站类

    实现快速充电站的具体功能，不支持V2G功能。快充站通常用于
    短时间停车场景，如高速公路服务区、商业区等，充电功率较高但不支持V2G。

    主要特点：
    - 支持快速充电，不支持V2G
    - 车辆充满电后自动离开
    - 支持排队等待机制
    - 充电功率较高
    """

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self._charging_vehicles: OrderedSet[str] = OrderedSet([])  # 正在充电的车辆
        self._waiting_queue: deque[str] = deque()  # 排队等待的车辆
        self._all_vehicles: set[str] = set()  # 充电站中的所有车辆

    def to_xml(self) -> str:
        """将快充站配置转换为XML格式"""
        ret = f'<fcs name="{self._name}" edge="{self._name}" slots="{self._slots}" bus="{self._node}" ' \
            f'x="{self._x}" y="{self._y}" max_pc="{self._max_power_per_slot * 3600:.2f}" pc_alloc="{self._power_alloc_method}">\n'
        ret += _get_price_xml(self._pbuy, "pbuy") + "\n"
        if len(self._offline) > 0:
            ret += self._offline.toXML("offline") + "\n"
        ret += "</fcs>"
        return ret
    
    def add_veh(self, veh_id: str) -> bool:
        """将车辆添加到快充站"""
        if veh_id in self._all_vehicles:
            return False

        if len(self._charging_vehicles) < self._slots:
            self._charging_vehicles.add(veh_id)
        else:
            self._waiting_queue.append(veh_id)

        self._all_vehicles.add(veh_id)
        return True

    def pop_veh(self, veh_id: str) -> bool:
        """从快充站移除车辆"""
        if veh_id not in self._all_vehicles:
            return False

        self._all_vehicles.remove(veh_id)

        try:
            self._charging_vehicles.remove(veh_id)
        except KeyError:
            try:
                self._waiting_queue.remove(veh_id)
            except ValueError:
                pass

        return True

    def __contains__(self, veh_id: str) -> bool:
        """检查快充站是否包含指定车辆"""
        return veh_id in self._all_vehicles

    def is_charging(self, veh_id: str) -> bool:
        """检查指定车辆是否正在充电"""
        return veh_id in self._charging_vehicles

    def veh_count(self, only_charging: bool = False) -> int:
        """返回快充站中车辆数量"""
        if only_charging:
            return len(self._charging_vehicles)
        else:
            return len(self._charging_vehicles) + len(self._waiting_queue)

    def vehicles(self):
        """返回快充站中所有车辆的迭代器"""
        return chain(self._charging_vehicles, self._waiting_queue)

    def averageSOC(self, ev_dict: EVDict, include_waiting: bool = True) -> float:
        """计算车辆的平均SOC"""
        total_vehicles = self.__len__()
        if total_vehicles == 0:
            return 0.0

        vehicle_list = chain(self._charging_vehicles, self._waiting_queue) if include_waiting else self._charging_vehicles
        return sum([ev_dict[vehicle_id].SOC for vehicle_id in vehicle_list]) / total_vehicles

    def wait_count(self) -> int:
        """等待充电的车辆数量"""
        return len(self._waiting_queue)

    def __len__(self) -> int:
        """返回快充站中车辆总数"""
        return len(self._charging_vehicles) + len(self._waiting_queue)

    def update(self, ev_dict: EVDict, sec: int, cur_time: int, v2g_k: float) -> list[str]:
        """
        更新快充站状态，处理充电

        Args:
            ev_dict: 车辆字典
            sec: 时间间隔（秒）
            cur_time: 当前时间
            v2g_k: V2G功率系数（快充站不使用此参数）

        Returns:
            充电完成并离开的车辆ID列表
        """
        # 快充站不支持V2G功能，忽略v2g_k参数
        _ = v2g_k
        if not self.is_online(cur_time):
            # 如果充电站故障，移除所有车辆
            departed_vehicles = list(chain(self._charging_vehicles, self._waiting_queue))
            self._charging_vehicles.clear()
            self._waiting_queue.clear()
            self._all_vehicles.clear()
            self._current_charging_load = 0.0
            return departed_vehicles

        total_charge_power = 0.0
        completed_vehicles = []

        if len(self._charging_vehicles) > 0:
            # 分配充电功率
            self._actual_power_limits = self._power_allocator(
                AllocationEnvironment(self, self._charging_vehicles, ev_dict, cur_time),
                len(self._charging_vehicles),
                self._max_power_per_slot,
                self._max_total_power
            )

            # 执行充电
            for i, vehicle_id in enumerate(self._charging_vehicles):
                vehicle = ev_dict[vehicle_id]
                actual_power = min(self._actual_power_limits[i], vehicle._efc_rate)
                total_charge_power += vehicle.charge(sec, self.pbuy(cur_time), actual_power)

                # 检查是否充电完成
                if vehicle.battery >= vehicle.charge_target:
                    completed_vehicles.append(vehicle_id)

        # 移除充电完成的车辆，并让等待的车辆开始充电
        for vehicle_id in completed_vehicles:
            self.pop_veh(vehicle_id)
            if len(self._waiting_queue) > 0:
                next_vehicle = self._waiting_queue.popleft()
                self._charging_vehicles.add(next_vehicle)

        self._current_charging_load = total_charge_power / sec
        return completed_vehicles

    def get_V2G_cap(self, ev_dict: EVDict, current_time: int) -> float:
        """快充站不支持V2G功能，始终返回0"""
        # 快充站不支持V2G，忽略参数
        _ = ev_dict, current_time
        return 0.0
